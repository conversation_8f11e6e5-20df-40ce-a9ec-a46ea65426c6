# 导管点云实时可视化系统

## 概述

本系统实现了导管点云的实时可视化，解决了原有系统中的SIGTRAP崩溃问题，并提供了线程安全、高性能的点云累积和显示功能。

## 主要功能

### 1. 体素化功能
- **方法**: `Catheter::voxelize(float spacing)`
- **功能**: 将PLY格式的导管数据进行体素化处理
- **实现**: 使用VTK的`vtkAdaptiveSubdivisionFilter`进行自适应细分

### 2. 坐标转换
- **方法**: `FAMVolume::mergeCatheterVolume(const Eigen::Isometry3f& transform, int scanPointIndex)`
- **功能**: 将导管体素化后的点云数据转换到世界坐标系
- **优化**: 使用临时容器收集数据，避免直接操作VTK对象

### 3. 点云累积
- **核心组件**: `PointCloudManager`类
- **功能**: 
  - 线程安全的点云数据存储
  - 自动去重避免重复点
  - 内存使用优化
  - 高效的数据访问接口

### 4. 可视化显示
- **核心组件**: `CatheterVisualizer`类
- **功能**:
  - 线程安全的可视化管理
  - 实时更新显示
  - 性能监控和优化
  - 绿色点云显示

### 5. 多线程安全
- **VTK对象保护**: 避免多线程直接访问VTK对象
- **数据同步**: 使用互斥锁保护共享数据
- **UI线程安全**: 确保UI更新在主线程中进行

## 核心类说明

### PointCloudManager
```cpp
class PointCloudManager
{
public:
    // 构造函数：maxPoints=最大点数，spatialTolerance=空间容差
    PointCloudManager(size_t maxPoints = 0, float spatialTolerance = 0.1f);
    
    // 添加新点云数据
    void addPoints(const std::vector<Eigen::Vector3f>& points, bool clearCurrent = true);
    
    // 获取当前帧点云
    bool getCurrentPoints(std::vector<Eigen::Vector3f>& points) const;
    
    // 获取所有累积点云
    bool getAllPoints(std::vector<Eigen::Vector3f>& points) const;
    
    // 清空所有数据
    void clear();
};
```

### CatheterVisualizer
```cpp
class CatheterVisualizer : public QObject
{
public:
    // 构造函数
    CatheterVisualizer(SceneWidget* sceneWidget, QObject* parent = nullptr);
    
    // 设置要可视化的Volume
    void setVolume(std::shared_ptr<FAMVolume> volume);
    
    // 启动实时可视化
    void startVisualization(int updateIntervalMs = 50);
    
    // 停止可视化
    void stopVisualization();
    
    // 设置点云属性
    void setPointCloudProperties(float pointSize, const Eigen::Vector4f& color, float opacity);
};
```

## 解决的问题

### 1. SIGTRAP崩溃问题
**原因分析**:
- VTK对象不是线程安全的
- 多线程环境下直接访问VTK对象导致内存冲突
- UI更新不在主线程中进行

**解决方案**:
- 使用临时容器收集数据，避免直接操作VTK对象
- 实现线程安全的数据传递机制
- 确保VTK对象的创建和更新在主线程中进行

### 2. 内存泄漏问题
**解决方案**:
- 实现智能内存管理
- 设置最大点数限制
- 自动清理过期数据

### 3. 性能优化
**优化措施**:
- 点云去重机制
- 下采样算法
- 批量数据处理
- 异步更新机制

## 使用方法

### 1. 初始化
```cpp
// 在MainWindow构造函数中
mCatheterVisualizer = std::make_unique<CatheterVisualizer>(this->sceneWidget(), this);

// 设置点云属性
mCatheterVisualizer->setPointCloudProperties(
    3.0f,  // 点大小
    Eigen::Vector4f(0.0f, 1.0f, 0.0f, 1.0f),  // 绿色
    1.0f   // 不透明
);

// 设置最大显示点数
mCatheterVisualizer->setMaxDisplayPoints(50000);
```

### 2. 启动可视化
```cpp
// 在startScan方法中
if (mCatheterVisualizer && this->mReconstructor) {
    mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
    mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
}
```

### 3. 停止可视化
```cpp
// 在析构函数或clear方法中
if (mCatheterVisualizer) {
    mCatheterVisualizer->stopVisualization();
    mCatheterVisualizer->clearVisualization();
}
```

## 配置参数

### 点云管理器参数
- **最大点数**: 100,000（可调整）
- **空间容差**: 0.5mm（用于去重）
- **更新间隔**: 50ms

### 可视化参数
- **点大小**: 3.0像素
- **颜色**: 绿色(0, 255, 0)
- **透明度**: 完全不透明

## 性能监控

系统提供了详细的性能统计信息：
- 总添加操作次数
- 过滤的重复点数量
- 平均添加时间
- 平均获取时间
- 内存使用情况

## 注意事项

1. **线程安全**: 所有VTK对象的操作必须在主线程中进行
2. **内存管理**: 定期监控内存使用，适当调整最大点数限制
3. **性能优化**: 根据实际需求调整更新间隔和下采样参数
4. **错误处理**: 系统包含完善的异常处理机制，确保稳定运行

## 编译要求

确保CMakeLists.txt包含以下文件：
- catheter_visualizer.cpp/h
- point_cloud_manager.cpp/h
- 相关的Qt和VTK依赖库

## 总结

本系统通过引入专门的点云管理和可视化组件，彻底解决了原有系统的崩溃问题，同时提供了更好的性能和用户体验。系统设计遵循了线程安全、内存高效和易于维护的原则。
