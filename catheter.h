#ifndef CATHETER_H
#define CATHETER_H

#include <string>
#include <unordered_map>
#ifndef EIGEN_USE_BLAS
	#define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>
#include <vtkPolyData.h>

class Catheter
{
public:
	//typedef std::shared_ptr<Catheter> Ptr;
	using Ptr= std::shared_ptr<Catheter> ;
	Catheter(const std::string& modelPath);
	~Catheter();

	vtkSmartPointer<vtkPolyData> polyData() const
	{
		return this->mPolyData;
	}
	
	const std::unordered_map<std::string, Eigen::Vector3f>& electrodes() const
	{
		return this->mElectrodes;
	}


	vtkSmartPointer<vtkPoints>Points()const
	{
		return this->mPoints;
	}


	vtkSmartPointer<vtkPoints> voxelize(float spacing);
private:
	std::string mModelPath;
	vtkSmartPointer<vtkPolyData> mPolyData;
	vtkSmartPointer<vtkPoints>mPoints;
	std::unordered_map<std::string, Eigen::Vector3f> mElectrodes;
};

#endif