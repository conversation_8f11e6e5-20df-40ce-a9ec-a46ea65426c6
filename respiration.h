#ifndef RESPIRATION_H
#define RESPIRATION_H

#include <vector>
#include <mutex>
#include <nanoflann.hpp>

class Respiration
{
public:
	struct Sample
	{
		float resp;
		double timestamp;
		float phase;
	};
	Respiration(int samplingRate);
	~Respiration();

	void addSample(float resp, double timestamp);
	const Sample& lookup(double timestamp);
	void estimatePhase(size_t start, size_t end);

    int samplingRate() const
    {
        return this->mSamplingRate;
    }

    const std::vector<Sample>& data() const {
        return this->mData;
    }

    int minNumToUpdate() const
    {
        return this->mSamplingRate / 2;
    }

    void clear();

    /** @name Interface expected by KDTreeSingleIndexAdaptor
     * @{ */

    /*const self_t& derived() const { return *this; }
    self_t& derived() { return *this; }*/

    // Must return the number of data points
    inline size_t kdtree_get_point_count() const { return mData.size(); }

    // Returns the dim'th component of the idx'th point in the class:
    inline double kdtree_get_pt(const size_t idx, const size_t dim) const
    {
        return mData[idx].timestamp;
    }

    // Optional bounding-box computation: return false to default to a standard
    // bbox computation loop.
    // Return true if the BBOX was already computed by the class and returned
    // in "bb" so it can be avoided to redo it again. Look at bb.size() to
    // find out the expected dimensionality (e.g. 2 or 3 for point clouds)
    template <class BBOX>
    bool kdtree_get_bbox(BBOX& /*bb*/) const
    {
        return false;
    }

private:
	using LUT1D = nanoflann::KDTreeSingleIndexDynamicAdaptor<
		nanoflann::L1_Adaptor<double, Respiration>,
        Respiration, 1 /* dim */>;
    
    std::recursive_timed_mutex mLock;
    int mSamplingRate;
    std::vector<Sample> mData;
	std::shared_ptr<LUT1D> mIndex;
    size_t mToBeIndexed;
};


#endif