#include "catheter_visualizer.h"
#include "scenewidget.h"
#include "volume.h"

#include <algorithm>
#include <random>
#include <QDebug>

#include <vtkVertexGlyphFilter.h>
#include <vtkUnsignedCharArray.h>
#include <vtkPointData.h>
#include <vtkProperty.h>

const std::string CatheterVisualizer::POINT_CLOUD_ACTOR_NAME = "catheter_points_safe";

CatheterVisualizer::CatheterVisualizer(SceneWidget* sceneWidget, QObject* parent)
    : QObject(parent)
    , mSceneWidget(sceneWidget)
    , mVolume(nullptr)
    , mUpdateTimer(new QTimer(this))
    , mIsActive(false)
    , mPointSize(2.0f)
    , mPointColor(0.0f, 1.0f, 0.0f, 1.0f)  // 绿色
    , mOpacity(1.0f)
    , mMaxDisplayPoints(0)  // 无限制
    , mCurrentPointCount(0)
    , mTotalPointCount(0)
    , mPointCloudActor(nullptr)
    , mCurrentPolyData(nullptr)
    , mUpdateCount(0)
{
    // 连接定时器
    connect(mUpdateTimer, &QTimer::timeout, this, &CatheterVisualizer::updateVisualization);
    
    // 设置定时器为单次触发模式，避免重叠更新
    mUpdateTimer->setSingleShot(false);
    
    mLastUpdateTime = std::chrono::steady_clock::now();
    mAverageUpdateTime = std::chrono::duration<double, std::milli>(0);
}

CatheterVisualizer::~CatheterVisualizer()
{
    stopVisualization();
    clearVisualization();
}

void CatheterVisualizer::setVolume(std::shared_ptr<FAMVolume> volume)
{
    std::lock_guard<std::mutex> lock(mStatsMutex);
    mVolume = volume;
    mCurrentPointCount = 0;
    mTotalPointCount = 0;
}

void CatheterVisualizer::startVisualization(int updateIntervalMs)
{
    if (!mSceneWidget || !mVolume) {
        qWarning() << "CatheterVisualizer: Cannot start visualization - missing SceneWidget or Volume";
        return;
    }
    
    mIsActive = true;
    mUpdateTimer->start(updateIntervalMs);
    qDebug() << "CatheterVisualizer: Started with update interval" << updateIntervalMs << "ms";
}

void CatheterVisualizer::stopVisualization()
{
    mIsActive = false;
    mUpdateTimer->stop();
    qDebug() << "CatheterVisualizer: Stopped";
}

void CatheterVisualizer::clearVisualization()
{
    if (mSceneWidget && mPointCloudActor) {
        try {
            mSceneWidget->removeDataSet(POINT_CLOUD_ACTOR_NAME);
        } catch (...) {
            qWarning() << "CatheterVisualizer: Error removing point cloud from scene";
        }
    }
    
    mPointCloudActor = nullptr;
    mCurrentPolyData = nullptr;
    
    std::lock_guard<std::mutex> lock(mStatsMutex);
    mCurrentPointCount = 0;
    mTotalPointCount = 0;
}

void CatheterVisualizer::setPointCloudProperties(float pointSize, 
                                                const Eigen::Vector4f& color, 
                                                float opacity)
{
    mPointSize = pointSize;
    mPointColor = color;
    mOpacity = opacity;
    
    // 如果actor已存在，立即应用新属性
    if (mPointCloudActor) {
        mPointCloudActor->GetProperty()->SetPointSize(mPointSize);
        mPointCloudActor->GetProperty()->SetColor(mPointColor[0], mPointColor[1], mPointColor[2]);
        mPointCloudActor->GetProperty()->SetOpacity(mOpacity);
    }
}

void CatheterVisualizer::setMaxDisplayPoints(size_t maxPoints)
{
    mMaxDisplayPoints = maxPoints;
}

size_t CatheterVisualizer::getCurrentPointCount() const
{
    std::lock_guard<std::mutex> lock(mStatsMutex);
    return mCurrentPointCount;
}

size_t CatheterVisualizer::getTotalPointCount() const
{
    std::lock_guard<std::mutex> lock(mStatsMutex);
    return mTotalPointCount;
}

void CatheterVisualizer::updateVisualization()
{
    if (!mIsActive || !mVolume || !mSceneWidget) {
        return;
    }
    
    auto startTime = std::chrono::steady_clock::now();
    
    try {
        // 从Volume获取最新的点云数据
        std::vector<Eigen::Vector3f> currentPoints, allPoints;
        if (!mVolume->getPointsCacheData(currentPoints, allPoints)) {
            // 没有新数据，跳过此次更新
            return;
        }
        
        // 应用下采样（如果需要）
        std::vector<Eigen::Vector3f> displayPoints = allPoints;
        if (mMaxDisplayPoints > 0 && allPoints.size() > mMaxDisplayPoints) {
            displayPoints = downsamplePoints(allPoints, mMaxDisplayPoints);
        }
        
        // 更新统计信息
        {
            std::lock_guard<std::mutex> lock(mStatsMutex);
            mCurrentPointCount = currentPoints.size();
            mTotalPointCount = allPoints.size();
        }
        
        // 创建VTK点云数据
        if (!displayPoints.empty()) {
            vtkSmartPointer<vtkPolyData> polyData = createPointCloudPolyData(displayPoints);
            updateScenePointCloud(polyData);
        } else {
            // 如果没有点，清除显示
            if (mPointCloudActor) {
                mSceneWidget->removeDataSet(POINT_CLOUD_ACTOR_NAME);
                mPointCloudActor = nullptr;
            }
        }
        
    } catch (const std::exception& e) {
        qWarning() << "CatheterVisualizer: Exception in updateVisualization:" << e.what();
    } catch (...) {
        qWarning() << "CatheterVisualizer: Unknown exception in updateVisualization";
    }
    
    // 更新性能统计
    auto endTime = std::chrono::steady_clock::now();
    auto updateDuration = std::chrono::duration_cast<std::chrono::duration<double, std::milli>>(endTime - startTime);
    
    mUpdateCount++;
    if (mUpdateCount == 1) {
        mAverageUpdateTime = updateDuration;
    } else {
        // 计算移动平均
        double alpha = 0.1; // 平滑因子
        mAverageUpdateTime = std::chrono::duration<double, std::milli>(
            alpha * updateDuration.count() + (1.0 - alpha) * mAverageUpdateTime.count());
    }
    
    mLastUpdateTime = endTime;
}

vtkSmartPointer<vtkPolyData> CatheterVisualizer::createPointCloudPolyData(const std::vector<Eigen::Vector3f>& points)
{
    if (points.empty()) {
        return nullptr;
    }
    
    // 创建VTK Points
    vtkSmartPointer<vtkPoints> vtkPoints = vtkSmartPointer<vtkPoints>::New();
    vtkPoints->SetNumberOfPoints(points.size());
    
    for (size_t i = 0; i < points.size(); ++i) {
        vtkPoints->SetPoint(i, points[i][0], points[i][1], points[i][2]);
    }
    
    // 创建PolyData
    vtkSmartPointer<vtkPolyData> polyData = vtkSmartPointer<vtkPolyData>::New();
    polyData->SetPoints(vtkPoints);
    
    // 创建顶点过滤器
    vtkSmartPointer<vtkVertexGlyphFilter> vertexFilter = vtkSmartPointer<vtkVertexGlyphFilter>::New();
    vertexFilter->SetInputData(polyData);
    vertexFilter->Update();
    
    // 设置颜色
    vtkSmartPointer<vtkUnsignedCharArray> colors = vtkSmartPointer<vtkUnsignedCharArray>::New();
    colors->SetNumberOfComponents(3);
    colors->SetName("Colors");
    
    unsigned char color[3] = {
        static_cast<unsigned char>(mPointColor[0] * 255),
        static_cast<unsigned char>(mPointColor[1] * 255),
        static_cast<unsigned char>(mPointColor[2] * 255)
    };
    
    for (vtkIdType i = 0; i < vertexFilter->GetOutput()->GetNumberOfPoints(); i++) {
        colors->InsertNextTypedTuple(color);
    }
    
    vertexFilter->GetOutput()->GetPointData()->SetScalars(colors);
    
    return vertexFilter->GetOutput();
}

void CatheterVisualizer::updateScenePointCloud(vtkSmartPointer<vtkPolyData> polyData)
{
    if (!polyData || !mSceneWidget) {
        return;
    }
    
    try {
        if (mPointCloudActor) {
            // 尝试更新现有actor
            if (mSceneWidget->updateDataSet(polyData, POINT_CLOUD_ACTOR_NAME)) {
                mCurrentPolyData = polyData;
                // 确保属性正确设置
                mPointCloudActor->GetProperty()->SetPointSize(mPointSize);
                mPointCloudActor->GetProperty()->SetOpacity(mOpacity);
            }
        } else {
            // 创建新的actor
            Eigen::Vector4d rgba(mPointColor[0], mPointColor[1], mPointColor[2], mOpacity);
            mPointCloudActor = mSceneWidget->addDataSet(polyData, POINT_CLOUD_ACTOR_NAME, rgba);
            
            if (mPointCloudActor) {
                mPointCloudActor->GetProperty()->SetPointSize(mPointSize);
                mCurrentPolyData = polyData;
            }
        }
    } catch (...) {
        qWarning() << "CatheterVisualizer: Error updating scene point cloud";
        // 如果更新失败，尝试重新创建
        mPointCloudActor = nullptr;
        mCurrentPolyData = nullptr;
    }
}

std::vector<Eigen::Vector3f> CatheterVisualizer::downsamplePoints(const std::vector<Eigen::Vector3f>& points, 
                                                                 size_t maxPoints)
{
    if (points.size() <= maxPoints) {
        return points;
    }
    
    std::vector<Eigen::Vector3f> result;
    result.reserve(maxPoints);
    
    // 使用均匀采样
    double step = static_cast<double>(points.size()) / maxPoints;
    for (size_t i = 0; i < maxPoints; ++i) {
        size_t index = static_cast<size_t>(i * step);
        if (index < points.size()) {
            result.push_back(points[index]);
        }
    }
    
    return result;
}
