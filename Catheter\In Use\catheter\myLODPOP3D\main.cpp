﻿#include "mainwindow.h"

#include <QApplication>


#ifndef INITIAL_OPENGL
  #define INITIAL_OPENGL
  #include <vtkAutoInit.h>
  VTK_MODULE_INIT(vtkRenderingOpenGL2)
  VTK_MODULE_INIT(vtkInteractionStyle)
  VTK_MODULE_INIT(vtkRenderingFreeType)
  #endif
  #include <iostream>
  using namespace std;
 #include <vtkSmartPointer.h>
 #include <vtkPolyDataMapper.h>
 #include <vtkLODProp3D.h>
 #include <vtkRenderWindow.h>
 #include <vtkRenderer.h>
 #include <vtkRenderWindowInteractor.h>
 #include <vtkPolyData.h>
 #include <vtkSphereSource.h>
 #include <vtkCallbackCommand.h>
 #include <vtkProperty.h>
 #include <vtkTriangle.h>
  #include <vtkPointData.h>
  #include <vtkCellData.h>


  #include <vtkProperty.h>

//#include <QApplication>
//#include <fstream>
 #include <sstream>
#include <iostream>
//#include <stdio.h>
#include <string.h>
#include <string>
using namespace std;

/*
 vtkSmartPointer<vtkRenderer> renderer=vtkSmartPointer<vtkRenderer>::New();

void RefreshCallback(vtkObject* caller,
                       long unsigned int eventId,
                       void* clientData,
                       void* callData)
  {
      vtkSmartPointer<vtkLODProp3D>lodProp=static_cast<vtkLODProp3D*>(clientData);
      cout<<"Last rendered LOD:"<<lodProp->GetLastRenderedLODID()<<endl;
      static_cast<vtkRenderWindowInteractor*>(caller)->Render();
         renderer->ResetCamera();
  }
*/
  int main()
  {

      //RGB Colors
      unsigned char vector1Color[3]={0,0,0};
     // unsigned char vector2Color[3]={0,0,0};
     // unsigned char vector3Color[3]={0,0,0};

       vtkSmartPointer<vtkUnsignedCharArray>pointsColors=vtkSmartPointer<vtkUnsignedCharArray>::New();
       pointsColors->SetNumberOfComponents(3);

       vtkSmartPointer<vtkPoints>points=vtkSmartPointer<vtkPoints>::New();
       vtkSmartPointer<vtkTriangle>triangle=vtkSmartPointer<vtkTriangle>::New();
       vtkSmartPointer<vtkPolyData>polydata=vtkSmartPointer<vtkPolyData>::New();
       vtkSmartPointer<vtkCellArray>triangles=vtkSmartPointer<vtkCellArray>::New();


      //for x y z
         std::string filename = "D:/3D/VTK/data/bazhuayu20240308.ply"; //bunny.txt  //leftAtrium20230226.txt xyz.txt poisson_20220722.txt appleVtk.txt  mouse.txt".//Chair.txt";bunny.txt;Horse.txt;Block.txt；result
         std::ifstream filestream(filename.c_str()); //文件流  //table_scene_mug_stereo_textured.pcd
         std::string line;//heart.txt                 //leftAtrium20230226.txt  mouse.txt   scandata20240108.txt

         unsigned int N,x,y,z, vectorIndex=0,COUNT;
         double vector1R,vector1G,vector1B;//,vector2R,vector2G,vector2B,vector3R,vector3G,vector3B;

         int index=0;
         int Count=0;
         unsigned int headCount=14;
         std::string str1,str2,str3;
         int vectorCount=0,meshCount=0;
         int temp=0;
        // std::stringstream linestream;
    while(std::getline(filestream, line))  //整行读取文件
       {

           index=index+1;
            std::stringstream linestream;
           linestream << line;
          // linestream >>str1>>str2 >>vectorCount;
        if(index<=headCount)
           {
               //    {
             //     linestream >>str1;
             //   if((index==1)&&(str1!="ply"))
             //     {
             //       break;
             //     }

                 // {
                if(index==4)
                {
                   linestream >>str1>>str2 >>temp;
                   if((str1=="element")&&(str2=="vertex"))
                     {
                       vectorCount=temp;
                     }
                }

                if(index==8)
                  {
                   linestream >>str1>>str2 >>temp;
                   if((str1=="element")&&(str2=="face"))
                      {
                       meshCount=temp;
                      }
                  }
            }//index <=headCount
         else
         {

             if((index>14)&&(index<=( vectorCount+headCount)))
                 {
                // vectorIndex=vectorIndex+1;
                 double x,y,z;
                 linestream >>x>>y>>z;
                 // temp=0;
                // points->
                 // vtkSmartPointer<vtkPoints>points=vtkSmartPointer<vtkPoints>::New();
                 temp= points->InsertNextPoint(x,y,z);
                 // polydata->SetPoints(points);

                //  temp=polydata->GetNumberOfPoints();
                 }
                 else
             //if(index>( vectorCount+headCount))
                  {
                // points->
                   unsigned int N,x,y,z, COUNT;
                  // double vector1R,vector1G,vector1B,vector2R,vector2G,vector2B,vector3R,vector3G,vector3B;
                   linestream >>N>>x>>y >>z>>vector1R>>vector1G>>vector1B>>COUNT;//>>vector2R>>vector2G>>vector2B>>vector3R>>vector3G>>vector3B;

                   Count=Count+1;
                   if( Count==600){
                       temp=0;
                       //break;
                   }
                   if( Count==30350){
                       temp=0;
                       //break;
                   }

                   if( Count==20350){
                       temp=0;
                       //break;
                   }
                   if( Count==10350){
                       temp=0;
                       //break;
                   }



                   //triangle->GetPoints()->SetPoint(0,1,2,3);


                   //Color
                   vector1Color[0]=round(vector1R);
                   vector1Color[1]=round(vector1G);
                   vector1Color[2]=round(vector1B);
/*
                   vector2Color[0]=round(vector2R*255);
                   vector2Color[1]=round(vector2G*255);
                   vector2Color[2]=round(vector2B*255);

                   vector3Color[0]=round(vector3R*255);
                   vector3Color[1]=round(vector3G*255);
                   vector3Color[2]=round(vector3B*255);
*/

 /*                  //Color
                   vector1Color[0]=(vector1R);
                   vector1Color[1]=(vector1G);
                   vector1Color[2]=(vector1B);

                   vector2Color[0]=(vector2R);
                   vector2Color[1]=(vector2G);
                   vector2Color[2]=(vector2B);

                   vector3Color[0]=(vector3R);
                   vector3Color[1]=(vector3G);
                   vector3Color[2]=(vector3B);
*/


                   //
                   triangle->GetPointIds()->SetId(0,x);
                   triangle->GetPointIds()->SetId(1,y);
                   triangle->GetPointIds()->SetId(2,z);
                   triangles->InsertNextCell(triangle);


                   //
                   pointsColors->InsertNextTypedTuple(vector1Color);
                 //  pointsColors->InsertNextTypedTuple(vector2Color);
                 //  pointsColors->InsertNextTypedTuple(vector3Color);

                 }
           }
      }

   //  polydata->SetPoints(points);
   //  temp=polydata->GetNumberOfPoints();


       polydata->SetPoints(points);
       polydata->SetPolys(triangles);
   // polydata->GetPointData()->SetScalars(pointsColors);
       polydata->GetCellData()->SetScalars(pointsColors);
   // temp=polydata->GetNumberOfCells();
   //Visualize
       vtkSmartPointer<vtkPolyDataMapper>mapper=vtkSmartPointer<vtkPolyDataMapper>::New();
       mapper->SetInputData(polydata);

       vtkSmartPointer<vtkActor>actor=vtkSmartPointer<vtkActor>::New();
       actor->SetMapper(mapper);

       vtkSmartPointer<vtkRenderer>renderer=vtkSmartPointer<vtkRenderer>::New();
       vtkSmartPointer<vtkRenderWindow>renderwindow=vtkSmartPointer<vtkRenderWindow>::New();
       renderwindow->AddRenderer(renderer);
       renderwindow->SetSize(1000, 1200);
       vtkSmartPointer<vtkRenderWindowInteractor>rendererWindowInteractor=vtkSmartPointer<vtkRenderWindowInteractor>::New();
       rendererWindowInteractor->SetRenderWindow(renderwindow);

       renderer->AddActor(actor);
       renderer->ResetCamera();
       renderer->SetBackground(0.0,0.0,1.0);
       renderwindow->Render();
       rendererWindowInteractor->Start();










/*
  //创建高分辨率的圆球
      vtkSmartPointer<vtkSphereSource> highResSphere=vtkSmartPointer<vtkSphereSource>::New();
      int res=100;
      highResSphere->SetThetaResolution(res);
      highResSphere->SetPhiResolution(res);
      highResSphere->Update();
      //高分辨率球的映射器
      vtkSmartPointer<vtkPolyDataMapper> highResMapper=vtkSmartPointer<vtkPolyDataMapper>::New();
      highResMapper->SetInputConnection(highResSphere->GetOutputPort());
  //创建低分辨率的圆球
      vtkSmartPointer<vtkSphereSource>lowResSphere=vtkSmartPointer<vtkSphereSource>::New();
      //低分辨率球的映射器
      vtkSmartPointer<vtkPolyDataMapper> lowResMapper=vtkSmartPointer<vtkPolyDataMapper>::New();
      lowResMapper->SetInputConnection(lowResSphere->GetOutputPort());
  //分别创建高、低分辨率属性property
      //低分辨率属性
      vtkSmartPointer<vtkProperty> lowResProperty=vtkSmartPointer<vtkProperty>::New();
      lowResProperty->SetDiffuseColor(0.89,0.81,0.34);
      lowResProperty->SetInterpolationToFlat();
      //高分辨率属性
      vtkSmartPointer<vtkProperty> highResProperty=vtkSmartPointer<vtkProperty>::New();
      highResProperty->SetDiffuseColor(1,0.3882,0.2784);
      highResProperty->SetInterpolationToFlat();
  //创建vtkLODProp3D
      vtkSmartPointer<vtkLODProp3D> prop=vtkSmartPointer<vtkLODProp3D>::New();
      prop->AddLOD(highResMapper,highResProperty,800);
      prop->AddLOD(lowResMapper,lowResProperty,8);

      std::cout<<"There are "<<prop->GetNumberOfLODs()<<" LODs"<<std::endl;

  //创建显示窗口
      //renderer、window和interactor
    //  vtkSmartPointer<vtkRenderer> renderer=vtkSmartPointer<vtkRenderer>::New();
      vtkSmartPointer<vtkRenderWindow> renderWindow=vtkSmartPointer<vtkRenderWindow>::New();
      vtkSmartPointer<vtkRenderWindowInteractor> renderWindowInteractor=vtkSmartPointer<vtkRenderWindowInteractor>::New();
      renderWindow->AddRenderer(renderer);
      renderWindowInteractor->SetRenderWindow(renderWindow);

      //用prop修改渲染时间
      prop->SetAllocatedRenderTime(10,renderer);
      renderer->AddActor(prop);
      //创建回调命令对象，并将自定义的函数设定为该对象的回调函数
      vtkSmartPointer<vtkCallbackCommand> refreshCallback=vtkSmartPointer<vtkCallbackCommand>::New();
      refreshCallback->SetCallback(RefreshCallback);
      refreshCallback->SetClientData(prop);

      //添加ModifiedEvent事件侦听器
//      renderWindow->AddObserver(vtkCommand::ModifiedEvent,refreshCallback);
//      renderWindowInteractor->Start();


          renderWindowInteractor->Initialize();
          renderWindowInteractor->CreateRepeatingTimer(100);
          renderWindowInteractor->AddObserver(vtkCommand::TimerEvent, refreshCallback);
          renderWindow->Render();
          renderWindowInteractor->Start();
*/
     return 0;
 }


//  vtkLODProp3D类添加Mapper的函数原型为：
//  int AddLOD(vtkMapper* m, vtkProperty* p, double time);
//  该函数有三个参数，前两个定义了Mapper对象及Property属性。第三个参数为一个时间大小，表示当前Mapper渲染时间的初始值。如果该时间设置为0，表示开始时立即渲染对象而不用估计当前Mapper的渲染时间。
//  完成初试渲染后，可以得到各种Mapper的渲染时间。在vtkLODProp3D对象渲染时，会首先选择那些渲染时间为0的Mapper来渲染场景。
//  当然，一般来说，渲染时间为零的情况比较少见。在上个例子中，因为两个Mapper渲染时间的初始值均设为0，所以程序一开始便会渲染两次。
//  2.3 vtkLODProp3D的应用前景
//  实际中，经常会应用LOD技术来使用各种方法来实现体绘制，在满足精度的要求下提高交互体验。
//  我们知道，vtkVolume负责组合体绘制管线，除了包含一个Mapper对象外，还需要用VTKVolumeProperty对象来设置体绘制的颜色映射，如不透明度传输函数、梯度不透明度传输函数、颜色传输函数和设置阴影效果。vtkLODProp3D最大的优势在于提供了一种LOD技术来集成多种Mapper对象，能够有效提高渲染效率的交互体验。
//  ————————————————
//  版权声明：本文为CSDN博主「沈子恒」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
//  原文链接：https://blog.csdn.net/shenziheng1/article/details/54917295



//  计算机图形学
//  专栏收录该内容
//  18 篇文章1 订阅
//  订阅专栏
//  vtk中大致有三种时间响应机制：

//  command/observer模式之callback function
//  vtk中的command/observer模式是最常用的，也是各种处理方式的基础。这里首先介绍如何使用callback function的方法：
//  void KeyPressCallbackFunction ( vtkObject* caller, long unsigned int eventId, void* clientData, void* callData )
//  {
//    vtkRenderWindowInteractor *iren = static_cast<vtkRenderWindowInteractor*>(caller);// 指向调用这个callback函数的对象
//    vtkBoxWidget *tempWidget = static_cast<vtkBoxWidget*>(clientData);
//    tempWidget->GetTransform(inverse);
//    vtkSmartPointer<vtkMyCallback> callback = vtkSmartPointer<vtkMyCallback>::New();
//    tempWidget->AddObserver(vtkCommand::InteractionEvent, callback);

//    std::cout << "Pressed: " << iren->GetKeySym() << std::endl;
//  }

//  int main()
//  {
//      //…
//    vtkSmartPointer<vtkCallbackCommand> keyPressCallback = vtkSmartPointer<vtkCallbackCommand>::New();
//    keyPressCallback->SetCallback(KeyPressCallbackFunction);
//    keyPressCallback->SetClientData(boxWidget);
//    iren->AddObserver(vtkCommand::KeyPressEvent, keyPressCallback); // 设置callback function和event的联系
//      //……
//  }
//   上述代码片段中，callback function是主要的调用方式。其中caller是调用该callback function的对象，通常是RenderWindowInteractor，或者其他VTKWidget类；eventId是处理的event代号；clientData是传递到该函数中的数据；callData是随着触发的event一起传递的数据。

//  2.command/observer模式之vtkCommand
//  这种方法与第一种方法的原理相同，只不过这里使用不是一个callback function，而是继承了vtkCommand类来实现自己需要的操作。具体的代码如下：
//  class vtkMyCallback : public vtkCommand
//  {
//  public:
//    static vtkMyCallback *New()
//    {
//        return new vtkMyCallback;
//    }
//    virtual void Execute(vtkObject *caller, unsigned long, void*)
//      {
//  <span style="white-space:pre">	</span>vtkSmartPointer<vtkTransform> t = vtkSmartPointer<vtkTransform>::New();
//        <span style="white-space:pre">	</span>vtkBoxWidget *widget = reinterpret_cast<vtkBoxWidget*>(caller);
//        <span style="white-space:pre">	</span>widget->GetTransform(t);
//      t->PreMultiply();
//      t->Concatenate(vtkLinearTransform::SafeDownCast(inverse->GetInverse()));
//        <span style="white-space:pre">	</span>widget->GetProp3D()->SetUserTransform(t);
//      }
//  };

//  int main()
//  {
//    // …...
//    vtkSmartPointer<vtkBoxWidget> boxWidget =
//      vtkSmartPointer<vtkBoxWidget>::New();
//    boxWidget->SetInteractor(iren);
//    boxWidget->SetPlaceFactor(1.0);

//    boxWidget->SetProp3D(coneActor);
//    boxWidget->PlaceWidget();
//    vtkSmartPointer<vtkMyCallback> callback =
//      vtkSmartPointer<vtkMyCallback>::New();
//    boxWidget->AddObserver(vtkCommand::InteractionEvent, callback);// 处理event和callback的联系

//    boxWidget->On();
//      //……
//  }

//  interactor style
//  第三种方法是继承已有的interactor style来对自己希望设置的事件作出对应的响应，该方法可以看作是对若干个event的集合，缺点是只能对RenderWindowInteractor进行设置。该方法的代码片段如下：
//  class MyStyle : public vtkInteractorStyleImage
//  {
//    public:
//      static MyStyle* New();
//      vtkTypeMacro(MyStyle, vtkInteractorStyleImage);

//      virtual void OnLeftButtonDown()
//      {
//        std::cout << "Pressed left mouse button." << std::endl;
//        // Forward events
//        vtkInteractorStyleImage::OnLeftButtonDown();
//      }

//      virtual void OnRightButtonDown()
//      {
//          std::cout << "Pressed right mouse button." << std::endl;
//      }

//      virtual void OnRightButtonUp()
//      {
//          std::cout << "Release right mouse button." << std::endl;
//      }

//      virtual void OnMouseMove()
//      {
//          int *pos = this->GetInteractor()->GetEventPosition();
//          std::cout << pos[0] << "	" << pos[1] << "	" << pos[2] << std::endl;
//      }

//      virtual void OnLeftButtonUp()
//      {
//          std::cout << "Release right mouse button." << std::endl;
//      }

//  };

//  参考文档：
//  http://www.vtk.org/Wiki/VTK/Tutorials/Callbacks
//  http://www.vtk.org/Wiki/VTK/Examples/Cxx/Interaction/DoubleClick
//  http://www.cs.rpi.edu/~cutler/classes/visualization/F10/lectures/03_interaction.pdf
//  http://www.vtk.org/Wiki/VTK/Examples/Cxx/Interaction/KeypressObserver

//  ————————————————
//  版权声明：本文为CSDN博主「menjiawan」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
//  原文链接：https://blog.csdn.net/menjiawan/article/details/47010221
