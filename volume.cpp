#define NOMINMAX

#include <tbb/tbb.h>

#include <vtkQuad.h>
#include <vtkUniformGrid.h>
#include <vtkPointData.h>
#include <vtkFlyingEdges3D.h>
#include <vtkDiscreteFlyingEdges3D.h>
#include <vtkSmoothPolyDataFilter.h>
#include <vtkPolyDataNormals.h>
#include <vtkPointData.h>
#include <vtkFloatArray.h>
#include <vtkCleanPolyData.h>
#include <vtkWindowedSincPolyDataFilter.h>
#include <vtkDecimatePro.h>
#include <vtkPolyDataNormals.h>
#include <vtkFillHolesFilter.h>
#include <vtkGenericDataArray.h>
#include <vtkPolyDataToImageStencil.h>
#include <vtkImageStencil.h>

#include <vtkImageGaussianSmooth.h>//  12/04 2024
#include<vtkDiscreteFlyingEdges3D.h>

#include "volume.h"

double temp1;
int temp = 0;
Eigen::Vector3f xx;
Eigen::Vector3d p6;
//vtkDataArray* scalars;
FAMVolume::FAMVolume(float spacing, Catheter::Ptr catheter, const Eigen::AlignedBox3f& bounds)
    : mBounds(bounds)
{
    this->mCatheter = catheter;
    this->mCatheterVolume = mCatheter->voxelize(spacing);
    //this->mCatheterVolume = mCatheter->Points();
    
    // 初始化导管世界坐标点存储
    this->mCatheterWorldPoints = vtkSmartPointer<vtkPoints>::New();
    
    // 初始化所有累积导管点存储
    this->mAllCatheterWorldPoints = vtkSmartPointer<vtkPoints>::New();

    // 初始化点云管理器
    this->mPointCloudManager = std::make_unique<PointCloudManager>(100000, 0.5f); // 最多10万个点，0.5mm容差
    
    Eigen::Vector3f dimf = bounds.sizes()/ spacing;
    Eigen::Vector3i dims = Eigen::ceil(dimf.array()).cast<int>();
    this->mIndex2World.setIdentity();
    this->mIndex2World.scale(spacing);
    Eigen::Vector3f origin = this->mBounds.corner(Eigen::AlignedBox3f::CornerType::Min);
    this->mIndex2World.pretranslate(origin);
    cout << "mIndex2World.pretranslate(origin)" << endl << this->mIndex2World.matrix() << endl << endl;
    xx = origin;
    assert((this->mIndex2World * Eigen::Vector3f::Zero()).isApprox(origin));
    this->mWorld2Index = mIndex2World.inverse();
 //   this->mLocalVolume = openvdb::Int32Grid::Ptr(new openvdb::Int32Grid(-1));
	//this->mLocalVolume->setTransform(mIndex2World);
 //   this->mGlobalVolume = openvdb::Int32Grid::Ptr(new openvdb::Int32Grid(-1));
 //   this->mGlobalVolume->setTransform(mIndex2World);
    this->mImage = vtkSmartPointer<vtkImageData>::New();
    this->mImage->SetSpacing(spacing, spacing, spacing);
    this->mImage->SetDimensions(dims.data());
    this->mImage->SetOrigin(origin[0], origin[1], origin[2]);
    this->mImage->AllocateScalars(VTK_INT, 1);               // 1  component  gray  ImageData
    this->mImage->GetPointData()->GetScalars()->Fill(-1);
    mModCount = 0;
  //   scalars = this->mImage->GetPointData()->GetScalars();
    this->mStartTimeStamp = std::chrono::steady_clock::now();


/*
    //p1 world
    double p1a = 0;
    double p1x = 1;
    double p1y = 1;
    Eigen::AngleAxisd rotzp1(p1a*3.14/180,Eigen::Vector3d::UnitZ());
    Eigen::Matrix3d rotp1 = rotzp1.toRotationMatrix();
    Eigen::Isometry3d p1;
    p1 = rotp1;
    p1.translation() = Eigen::Vector3d(p1x, p1y,0);

    //p2 world
    double p2a = 45;
    double p2x = 2;
    double p2y = 2;
    Eigen::AngleAxisd rotzp2(p2a * 3.14 / 180, Eigen::Vector3d::UnitZ());
    Eigen::Matrix3d rotp2 = rotzp2.toRotationMatrix();
    Eigen::Isometry3d p2;
    p2 = rotp2;
    p2.translation() = Eigen::Vector3d(p2x, p2y, 0);

    //t12
    Eigen::Isometry3d t12 = p1.inverse() * p2;
    Eigen::Isometry3d t21 = p2.inverse() * p1;
    Eigen::Matrix3d r12m = t12.rotation();
    Eigen::Vector3d vt12 = t12.translation();
    Eigen::Vector3d eulerAngle = r12m.eulerAngles(0,1,2);


    //calculate p2 world position
    Eigen::Isometry3d p3 = p1 * t12;
    Eigen::Isometry3d p4 = p2 * t21;
    Eigen::Matrix3d p2rm = p3.rotation();
    Eigen::Vector3d p2t = p3.translation();
    Eigen::Vector3d eulerAngle1 = p2rm.eulerAngles(0, 1, 2);
    auto degree = 180 * eulerAngle1 / 3.1416;

    

//Different coordinate transformation
  
    Eigen::Quaterniond  q1(0.35, 0.2, 0.3, 0.1), q2(-0.5,0.4,-0.1,0.2);
    q1.normalize();
    q2.normalize();

    Eigen::Vector3d t1(0.3, 0.1, 0.1), t2(-0.1,0.5,0.3);
    Eigen::Vector3d p1_1(0.5,0,0.2);

    Eigen::Isometry3d Tw1(q1), Tw2(q2);
    Tw1.pretranslate(t1);
    Tw2.pretranslate(t2);
     p6 = q2 * q1.inverse() * (p1_1 - t1) + t2;
   // Eigen::Vector3d p8 = p6.transpose();
    Eigen::Vector3d p8 = Tw2 * Tw1.inverse() * p1_1;
    p6 = p8;
   */
}

FAMVolume::~FAMVolume()
{

}

float FAMVolume::spacing() const
{
    return (float)this->mIndex2World.data()[0];
}

Eigen::Vector3f FAMVolume::origin() const
{
    return this->mBounds.corner(Eigen::AlignedBox3f::CornerType::Min);
}

Eigen::Vector3f FAMVolume::center() const
{
    return this->mBounds.center();
}

void FAMVolume::setCenter(const Eigen::Vector3f& newCenter)
{
    auto translation = newCenter - this->center();
    this->mBounds.translate(translation);
    this->mIndex2World.pretranslate(translation);
    this->mWorld2Index = mIndex2World.inverse();
    Eigen::Vector3f origin = this->mBounds.corner(Eigen::AlignedBox3f::CornerType::Min);
    this->mImage->SetOrigin(origin[0], origin[1], origin[2]);
}
Eigen::Vector3i corrd;
Eigen::Isometry3f transform_inverse;
Eigen::Vector3d worldPoint1;
void FAMVolume::mergeCatheterVolume(const Eigen::Isometry3f& transform, int scanPointIndex)
{
    //transform_inverse = transform.inverse();
    int dims[3];
    this->mImage->GetDimensions(dims);
    //int* voxel = (int*)this->mImage->GetScalarPointer();
    unsigned int* voxel = (unsigned int*)this->mImage->GetScalarPointer();
    size_t numOfPoints = this->mCatheterVolume->GetNumberOfPoints();
    temp = numOfPoints;

    // 使用临时容器收集点数据，避免直接操作VTK对象
    std::vector<Eigen::Vector3f> currentFramePoints;
    std::vector<Eigen::Vector3f> newWorldPoints;
    currentFramePoints.reserve(numOfPoints);
    newWorldPoints.reserve(numOfPoints);

    for (size_t i = 0; i < numOfPoints; i++) {
        Eigen::Vector3d modelPoint;
        this->mCatheterVolume->GetPoint(i, modelPoint.data());
        worldPoint1 = modelPoint;
        Eigen::Vector3f worldPoint = transform * modelPoint.cast<float>();
      //  Eigen::Vector3f worldPoint1 = transform.inverse()* modelPoint.cast<float>();

        // 收集当前帧的点
        currentFramePoints.push_back(worldPoint);
        newWorldPoints.push_back(worldPoint);

        if (this->mBounds.contains(worldPoint))
        {
            Eigen::Vector3i globalCoord = (this->mWorld2Index * worldPoint).cast<int>();
            //int index = this->mLocalImage->GetScalarIndex(globalCoord.data());
            corrd = globalCoord;
            size_t index = globalCoord[2] * dims[0] * dims[1] + globalCoord[1] * dims[0] + globalCoord[0];
            voxel[index] = scanPointIndex;
            //if (this->mCoordMap.find(index) == this->mCoordMap.end())
            //{
            //    this->mCoordMap[index] = this->mCoords.size();
            //    this->mCoords.push_back(globalCoord);
            //}
        }
    }

    // 线程安全地更新VTK点云数据
    {
        std::lock_guard<std::recursive_mutex> lock(this->mCatheterPointsMutex);
        this->mCatheterWorldPoints->Reset();
        for (const auto& point : currentFramePoints) {
            this->mCatheterWorldPoints->InsertNextPoint(point[0], point[1], point[2]);
        }
    }

    // 线程安全地累积所有点云数据
    {
        std::lock_guard<std::recursive_mutex> allLock(this->mAllCatheterPointsMutex);
        for (const auto& point : newWorldPoints) {
            this->mAllCatheterWorldPoints->InsertNextPoint(point[0], point[1], point[2]);
        }
    }

    // 使用点云管理器更新数据
    if (mPointCloudManager) {
        mPointCloudManager->addPoints(newWorldPoints, true);
    }

    //if (this->mCoords.size() - this->mToBeIndexed > 0)
    //{
    //    this->mTree->addPoints(this->mToBeIndexed, this->mCoords.size() - 1);
    //    this->mToBeIndexed = this->mCoords.size();
    //}
}

void FAMVolume::clear()
{
    this->mImage->GetPointData()->GetScalars()->Fill(-1);

    // 清除累积的导管点
    std::lock_guard<std::recursive_mutex> lock(this->mAllCatheterPointsMutex);
    this->mAllCatheterWorldPoints->Reset();

    // 清除点云管理器数据
    if (mPointCloudManager) {
        mPointCloudManager->clear();
    }
}

vtkSmartPointer<vtkPolyData> FAMVolume::reconstruct(vtkSmartPointer<vtkImageData> image, std::recursive_mutex& mutex)
{
   // int smoother_niter = std::max(10, int(100 / this->spacing()));
    int smoother_niter = std::max(10, int(50/ this->spacing()));
    vtkSmartPointer<vtkFlyingEdges3D> isoSurface = vtkSmartPointer<vtkFlyingEdges3D>::New();
   // vtkSmartPointer<vtkDiscreteFlyingEdges3D> flyingEdges = vtkSmartPointer<vtkDiscreteFlyingEdges3D>::New();
  
    isoSurface->SetInputData(image);
    isoSurface->ComputeNormalsOff();
    isoSurface->ComputeGradientsOff();
    isoSurface->SetArrayComponent(0);
    isoSurface->SetNumberOfContours(1);
    isoSurface->SetValue(0, 1.0);
    isoSurface->SetComputeScalars(true);
    isoSurface->Update();

    //
 //   vtkSmartPointer<vtkFillHolesFilter> fillHoles(vtkFillHolesFilter::New());
  //  fillHoles->SetInputConnection(isoSurface->GetOutputPort());
 //   fillHoles->SetHoleSize(10.0);
 //   fillHoles->Update();


    //


    vtkSmartPointer<vtkWindowedSincPolyDataFilter> sincFilter = vtkSmartPointer<vtkWindowedSincPolyDataFilter>::New();

   // sincFilter->SetInputConnection(fillHoles->GetOutputPort());
    sincFilter->SetInputConnection(isoSurface->GetOutputPort());
    sincFilter->SetNumberOfIterations(smoother_niter);
    sincFilter->FeatureEdgeSmoothingOff();
    sincFilter->NonManifoldSmoothingOn();
    sincFilter->SetFeatureAngle(120);//30
    sincFilter->SetEdgeAngle(10);
    //smoother->BoundarySmoothingOn();
    sincFilter->BoundarySmoothingOff();
    //smoother->SetPassBand(std::min(0.2, 0.05 + this->spacing() * 0.02));
    sincFilter->SetPassBand(std::min(0.2, 0.01 + this->spacing() * 0.005));//
    //smoother->NormalizeCoordinatesOn();
    sincFilter->Update();


    vtkSmartPointer<vtkPolyDataNormals> normals = vtkSmartPointer<vtkPolyDataNormals>::New();
    normals->SetInputConnection(sincFilter->GetOutputPort());
   // normals->SetInputConnection(flyingEdges->GetOutputPort());
    normals->AutoOrientNormalsOff();
    normals->ComputePointNormalsOn();
    normals->ComputeCellNormalsOff();
    normals->ConsistencyOn();
    normals->FlipNormalsOff();
    normals->NonManifoldTraversalOn();
    normals->SplittingOff();
    normals->SetFeatureAngle(30.0);
    normals->Update();

    return normals->GetOutput();



}

vtkSmartPointer<vtkPolyData> FAMVolume::reconstruct()
{    
    return this->reconstruct(this->mImage, this->mImageMutex);
}

void FAMVolume::addScanPoint2LocalVolume(const FAMPoint& fpt, int scanPointIndex)
{
    if (!this->mBounds.contains(fpt.catheterCoord))
        return;

    std::lock_guard<std::recursive_mutex> localLock(this->mImageMutex);
    if (this->mLastPt.timestamp > 0)
    {
        // interpolating
        float d = (fpt.catheterCoord - mLastPt.catheterCoord).norm();
        temp1 = d;
        temp = this->spacing();
        if((d>2*this->spacing())&&(d <20* this->spacing()))
         //if(0)
        {
           double spacing = this->spacing();
           double numToInterpolate = ceil(d / spacing);// ****;
           tbb::parallel_for(
           tbb::blocked_range<int>(0, numToInterpolate ),//****
            [&](const tbb::blocked_range<int>& r)
            {
                for (int i = r.begin(); i != r.end(); ++i)
                {
                    FAMPoint pt = mLastPt.interpolate(i / numToInterpolate, fpt);
                    this->mergeCatheterVolume(pt.transform(), scanPointIndex);
                }
            }
           );
        }//if
        else
        if (d <= 2 * this->spacing())
        {
            this->mergeCatheterVolume(fpt.transform(), scanPointIndex);
        }
    }
    else
    {
        this->mergeCatheterVolume(fpt.transform(), scanPointIndex);
    }
    mLastPt = fpt;
}

void FAMVolume::mergeMesh(vtkSmartPointer<vtkPolyData> mesh)
{
    std::lock_guard<std::recursive_mutex> localLock(this->mImageMutex);
    vtkSmartPointer<vtkPolyDataToImageStencil> polyStencil = vtkSmartPointer<vtkPolyDataToImageStencil>::New();
    polyStencil->SetInputData(mesh);
    polyStencil->SetOutputOrigin(this->mImage->GetOrigin());
    polyStencil->SetOutputSpacing(this->mImage->GetSpacing());
    polyStencil->SetOutputWholeExtent(this->mImage->GetExtent());
    polyStencil->Update();

    if (!this->mWhiteImage)
    {
        this->mWhiteImage = vtkSmartPointer<vtkImageData>::New();
        this->mWhiteImage->SetSpacing(this->mImage->GetSpacing());
        this->mWhiteImage->SetOrigin(this->mImage->GetOrigin());
        this->mWhiteImage->SetExtent(this->mImage->GetExtent());
        this->mWhiteImage->AllocateScalars(VTK_UNSIGNED_CHAR, 1);
        this->mWhiteImage->GetPointData()->GetScalars()->Fill(255);
    }

    vtkSmartPointer<vtkImageStencil> imageStencil = vtkSmartPointer<vtkImageStencil>::New();
    imageStencil->SetInputData(this->mWhiteImage);
    imageStencil->SetStencilConnection(polyStencil->GetOutputPort());
    imageStencil->SetBackgroundInputData(this->mImage);
    imageStencil->Update();


    vtkIdType count =mesh->GetNumberOfCells();
    temp = count;
    count = mesh->GetNumberOfPoints();
    temp = count;
    mModCount++;

  //  if(modCount % 200 ==0)
 //   {
  
  //  modCount=0��
   //  13/04 2024
  //if(count >16000)
    
  if(mModCount % 30== 0)//  48
  {
    vtkSmartPointer<vtkImageGaussianSmooth> gaussianSmooth = vtkSmartPointer<vtkImageGaussianSmooth>::New();
    gaussianSmooth->SetInputConnection(imageStencil->GetOutputPort());
    gaussianSmooth->SetDimensionality(3);
    gaussianSmooth->SetRadiusFactor(3);//5
    //gaussianSmooth->SetRadiusFactors(5,5,5);
    gaussianSmooth->SetStandardDeviation(0.3);//1
    //gaussianSmooth->SetStandardDeviations(1,1,1);
    gaussianSmooth->Update();
    this->mImage= gaussianSmooth->GetOutput();
    mModCount = 0;
  }
  else 
  
  {
      this->mImage = imageStencil->GetOutput();
  }
   
}

vtkSmartPointer<vtkPoints> FAMVolume::catheterWorldPoints() const
{
    std::lock_guard<std::recursive_mutex> lock(this->mCatheterPointsMutex);
    // 创建新的VTK Points对象，避免直接返回内部对象
    vtkSmartPointer<vtkPoints> points = vtkSmartPointer<vtkPoints>::New();
    if (this->mCatheterWorldPoints && this->mCatheterWorldPoints->GetNumberOfPoints() > 0) {
        points->DeepCopy(this->mCatheterWorldPoints);
    }
    return points;
}

vtkSmartPointer<vtkPoints> FAMVolume::allCatheterWorldPoints() const
{
    std::lock_guard<std::recursive_mutex> lock(this->mAllCatheterPointsMutex);
    // 创建新的VTK Points对象，避免直接返回内部对象
    vtkSmartPointer<vtkPoints> points = vtkSmartPointer<vtkPoints>::New();
    if (this->mAllCatheterWorldPoints && this->mAllCatheterWorldPoints->GetNumberOfPoints() > 0) {
        points->DeepCopy(this->mAllCatheterWorldPoints);
    }
    return points;
}

void FAMVolume::updatePointsCache()
{
    // 这个方法现在由点云管理器处理，保留为兼容性
}

bool FAMVolume::getPointsCacheData(std::vector<Eigen::Vector3f>& currentPoints,
                                  std::vector<Eigen::Vector3f>& allPoints)
{
    if (!mPointCloudManager) {
        return false;
    }

    // 使用点云管理器获取数据
    bool hasCurrentData = mPointCloudManager->getCurrentPoints(currentPoints);
    bool hasAllData = mPointCloudManager->getAllPoints(allPoints);

    return hasCurrentData || hasAllData;
}
