ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2023-07-13T17:25:54',('Author'),(
    ''),'Open CASCADE STEP processor 7.6','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('catheter_1','catheter_1','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23,#27,#31,#35,#39,#43,#47,
    #51),#55);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,-6.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,-7.));
#21 = DIRECTION('',(0.,0.,1.));
#22 = DIRECTION('',(1.,0.,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,-15.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = AXIS2_PLACEMENT_3D('',#28,#29,#30);
#28 = CARTESIAN_POINT('',(0.,0.,-19.));
#29 = DIRECTION('',(0.,0.,1.));
#30 = DIRECTION('',(1.,0.,0.));
#31 = AXIS2_PLACEMENT_3D('',#32,#33,#34);
#32 = CARTESIAN_POINT('',(0.,0.,-24.));
#33 = DIRECTION('',(0.,0.,1.));
#34 = DIRECTION('',(1.,0.,0.));
#35 = AXIS2_PLACEMENT_3D('',#36,#37,#38);
#36 = CARTESIAN_POINT('',(0.,0.,-25.));
#37 = DIRECTION('',(0.,0.,1.));
#38 = DIRECTION('',(1.,0.,0.));
#39 = AXIS2_PLACEMENT_3D('',#40,#41,#42);
#40 = CARTESIAN_POINT('',(0.,0.,-30.));
#41 = DIRECTION('',(0.,0.,1.));
#42 = DIRECTION('',(1.,0.,0.));
#43 = AXIS2_PLACEMENT_3D('',#44,#45,#46);
#44 = CARTESIAN_POINT('',(0.,0.,0.));
#45 = DIRECTION('',(0.,0.,1.));
#46 = DIRECTION('',(1.,0.,0.));
#47 = AXIS2_PLACEMENT_3D('',#48,#49,#50);
#48 = CARTESIAN_POINT('',(0.,0.,0.));
#49 = DIRECTION('',(0.,0.,1.));
#50 = DIRECTION('',(-0.5,0.866025403784,0.));
#51 = AXIS2_PLACEMENT_3D('',#52,#53,#54);
#52 = CARTESIAN_POINT('',(0.,0.,0.));
#53 = DIRECTION('',(0.,0.,1.));
#54 = DIRECTION('',(-0.5,-0.866025403784,0.));
#55 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#59)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#56,#57,#58)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#56 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#57 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#58 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#59 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#56,
  'distance_accuracy_value','confusion accuracy');
#60 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#61 = SHAPE_DEFINITION_REPRESENTATION(#62,#68);
#62 = PRODUCT_DEFINITION_SHAPE('','',#63);
#63 = PRODUCT_DEFINITION('design','',#64,#67);
#64 = PRODUCT_DEFINITION_FORMATION('','',#65);
#65 = PRODUCT('link_001','link_001','',(#66));
#66 = PRODUCT_CONTEXT('',#2,'mechanical');
#67 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#68 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#69),#167);
#69 = MANIFOLD_SOLID_BREP('',#70);
#70 = CLOSED_SHELL('',(#71,#159,#163));
#71 = ADVANCED_FACE('',(#72),#86,.T.);
#72 = FACE_BOUND('',#73,.F.);
#73 = EDGE_LOOP('',(#74,#104,#131,#132));
#74 = ORIENTED_EDGE('',*,*,#75,.T.);
#75 = EDGE_CURVE('',#76,#78,#80,.T.);
#76 = VERTEX_POINT('',#77);
#77 = CARTESIAN_POINT('',(1.165,0.,4.));
#78 = VERTEX_POINT('',#79);
#79 = CARTESIAN_POINT('',(1.165,0.,6.));
#80 = SEAM_CURVE('',#81,(#85,#97),.PCURVE_S1.);
#81 = LINE('',#82,#83);
#82 = CARTESIAN_POINT('',(1.165,0.,4.));
#83 = VECTOR('',#84,1.);
#84 = DIRECTION('',(0.,0.,1.));
#85 = PCURVE('',#86,#91);
#86 = CYLINDRICAL_SURFACE('',#87,1.165);
#87 = AXIS2_PLACEMENT_3D('',#88,#89,#90);
#88 = CARTESIAN_POINT('',(0.,0.,4.));
#89 = DIRECTION('',(-0.,-0.,-1.));
#90 = DIRECTION('',(1.,0.,0.));
#91 = DEFINITIONAL_REPRESENTATION('',(#92),#96);
#92 = LINE('',#93,#94);
#93 = CARTESIAN_POINT('',(-0.,0.));
#94 = VECTOR('',#95,1.);
#95 = DIRECTION('',(-0.,-1.));
#96 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#97 = PCURVE('',#86,#98);
#98 = DEFINITIONAL_REPRESENTATION('',(#99),#103);
#99 = LINE('',#100,#101);
#100 = CARTESIAN_POINT('',(-6.28318530718,0.));
#101 = VECTOR('',#102,1.);
#102 = DIRECTION('',(-0.,-1.));
#103 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#104 = ORIENTED_EDGE('',*,*,#105,.T.);
#105 = EDGE_CURVE('',#78,#78,#106,.T.);
#106 = SURFACE_CURVE('',#107,(#112,#119),.PCURVE_S1.);
#107 = CIRCLE('',#108,1.165);
#108 = AXIS2_PLACEMENT_3D('',#109,#110,#111);
#109 = CARTESIAN_POINT('',(0.,0.,6.));
#110 = DIRECTION('',(0.,0.,1.));
#111 = DIRECTION('',(1.,0.,0.));
#112 = PCURVE('',#86,#113);
#113 = DEFINITIONAL_REPRESENTATION('',(#114),#118);
#114 = LINE('',#115,#116);
#115 = CARTESIAN_POINT('',(-0.,-2.));
#116 = VECTOR('',#117,1.);
#117 = DIRECTION('',(-1.,0.));
#118 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#119 = PCURVE('',#120,#125);
#120 = PLANE('',#121);
#121 = AXIS2_PLACEMENT_3D('',#122,#123,#124);
#122 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,6.));
#123 = DIRECTION('',(0.,0.,1.));
#124 = DIRECTION('',(1.,0.,0.));
#125 = DEFINITIONAL_REPRESENTATION('',(#126),#130);
#126 = CIRCLE('',#127,1.165);
#127 = AXIS2_PLACEMENT_2D('',#128,#129);
#128 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#129 = DIRECTION('',(1.,0.));
#130 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#131 = ORIENTED_EDGE('',*,*,#75,.F.);
#132 = ORIENTED_EDGE('',*,*,#133,.F.);
#133 = EDGE_CURVE('',#76,#76,#134,.T.);
#134 = SURFACE_CURVE('',#135,(#140,#147),.PCURVE_S1.);
#135 = CIRCLE('',#136,1.165);
#136 = AXIS2_PLACEMENT_3D('',#137,#138,#139);
#137 = CARTESIAN_POINT('',(0.,0.,4.));
#138 = DIRECTION('',(0.,0.,1.));
#139 = DIRECTION('',(1.,0.,0.));
#140 = PCURVE('',#86,#141);
#141 = DEFINITIONAL_REPRESENTATION('',(#142),#146);
#142 = LINE('',#143,#144);
#143 = CARTESIAN_POINT('',(-0.,0.));
#144 = VECTOR('',#145,1.);
#145 = DIRECTION('',(-1.,0.));
#146 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#147 = PCURVE('',#148,#153);
#148 = PLANE('',#149);
#149 = AXIS2_PLACEMENT_3D('',#150,#151,#152);
#150 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,4.));
#151 = DIRECTION('',(0.,0.,1.));
#152 = DIRECTION('',(1.,0.,0.));
#153 = DEFINITIONAL_REPRESENTATION('',(#154),#158);
#154 = CIRCLE('',#155,1.165);
#155 = AXIS2_PLACEMENT_2D('',#156,#157);
#156 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#157 = DIRECTION('',(1.,0.));
#158 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#159 = ADVANCED_FACE('',(#160),#148,.F.);
#160 = FACE_BOUND('',#161,.F.);
#161 = EDGE_LOOP('',(#162));
#162 = ORIENTED_EDGE('',*,*,#133,.T.);
#163 = ADVANCED_FACE('',(#164),#120,.T.);
#164 = FACE_BOUND('',#165,.T.);
#165 = EDGE_LOOP('',(#166));
#166 = ORIENTED_EDGE('',*,*,#105,.T.);
#167 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#171)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#168,#169,#170)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#168 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#169 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#170 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#171 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#168,
  'distance_accuracy_value','confusion accuracy');
#172 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#173,#175);
#173 = ( REPRESENTATION_RELATIONSHIP('','',#68,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#174) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#174 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#175 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#176
  );
#176 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('1','link_001','',#5,#63,$);
#177 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#65));
#178 = SHAPE_DEFINITION_REPRESENTATION(#179,#185);
#179 = PRODUCT_DEFINITION_SHAPE('','',#180);
#180 = PRODUCT_DEFINITION('design','',#181,#184);
#181 = PRODUCT_DEFINITION_FORMATION('','',#182);
#182 = PRODUCT('electrode_001','electrode_001','',(#183));
#183 = PRODUCT_CONTEXT('',#2,'mechanical');
#184 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#185 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#186),#284);
#186 = MANIFOLD_SOLID_BREP('',#187);
#187 = CLOSED_SHELL('',(#188,#276,#280));
#188 = ADVANCED_FACE('',(#189),#203,.T.);
#189 = FACE_BOUND('',#190,.F.);
#190 = EDGE_LOOP('',(#191,#221,#248,#249));
#191 = ORIENTED_EDGE('',*,*,#192,.T.);
#192 = EDGE_CURVE('',#193,#195,#197,.T.);
#193 = VERTEX_POINT('',#194);
#194 = CARTESIAN_POINT('',(1.165,0.,4.));
#195 = VERTEX_POINT('',#196);
#196 = CARTESIAN_POINT('',(1.165,0.,5.));
#197 = SEAM_CURVE('',#198,(#202,#214),.PCURVE_S1.);
#198 = LINE('',#199,#200);
#199 = CARTESIAN_POINT('',(1.165,0.,4.));
#200 = VECTOR('',#201,1.);
#201 = DIRECTION('',(0.,0.,1.));
#202 = PCURVE('',#203,#208);
#203 = CYLINDRICAL_SURFACE('',#204,1.165);
#204 = AXIS2_PLACEMENT_3D('',#205,#206,#207);
#205 = CARTESIAN_POINT('',(0.,0.,4.));
#206 = DIRECTION('',(-0.,-0.,-1.));
#207 = DIRECTION('',(1.,0.,0.));
#208 = DEFINITIONAL_REPRESENTATION('',(#209),#213);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(-0.,0.));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(-0.,-1.));
#213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#214 = PCURVE('',#203,#215);
#215 = DEFINITIONAL_REPRESENTATION('',(#216),#220);
#216 = LINE('',#217,#218);
#217 = CARTESIAN_POINT('',(-6.28318530718,0.));
#218 = VECTOR('',#219,1.);
#219 = DIRECTION('',(-0.,-1.));
#220 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#221 = ORIENTED_EDGE('',*,*,#222,.T.);
#222 = EDGE_CURVE('',#195,#195,#223,.T.);
#223 = SURFACE_CURVE('',#224,(#229,#236),.PCURVE_S1.);
#224 = CIRCLE('',#225,1.165);
#225 = AXIS2_PLACEMENT_3D('',#226,#227,#228);
#226 = CARTESIAN_POINT('',(0.,0.,5.));
#227 = DIRECTION('',(0.,0.,1.));
#228 = DIRECTION('',(1.,0.,0.));
#229 = PCURVE('',#203,#230);
#230 = DEFINITIONAL_REPRESENTATION('',(#231),#235);
#231 = LINE('',#232,#233);
#232 = CARTESIAN_POINT('',(-0.,-1.));
#233 = VECTOR('',#234,1.);
#234 = DIRECTION('',(-1.,0.));
#235 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#236 = PCURVE('',#237,#242);
#237 = PLANE('',#238);
#238 = AXIS2_PLACEMENT_3D('',#239,#240,#241);
#239 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,5.));
#240 = DIRECTION('',(0.,0.,1.));
#241 = DIRECTION('',(1.,0.,0.));
#242 = DEFINITIONAL_REPRESENTATION('',(#243),#247);
#243 = CIRCLE('',#244,1.165);
#244 = AXIS2_PLACEMENT_2D('',#245,#246);
#245 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#246 = DIRECTION('',(1.,0.));
#247 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#248 = ORIENTED_EDGE('',*,*,#192,.F.);
#249 = ORIENTED_EDGE('',*,*,#250,.F.);
#250 = EDGE_CURVE('',#193,#193,#251,.T.);
#251 = SURFACE_CURVE('',#252,(#257,#264),.PCURVE_S1.);
#252 = CIRCLE('',#253,1.165);
#253 = AXIS2_PLACEMENT_3D('',#254,#255,#256);
#254 = CARTESIAN_POINT('',(0.,0.,4.));
#255 = DIRECTION('',(0.,0.,1.));
#256 = DIRECTION('',(1.,0.,0.));
#257 = PCURVE('',#203,#258);
#258 = DEFINITIONAL_REPRESENTATION('',(#259),#263);
#259 = LINE('',#260,#261);
#260 = CARTESIAN_POINT('',(-0.,0.));
#261 = VECTOR('',#262,1.);
#262 = DIRECTION('',(-1.,0.));
#263 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#264 = PCURVE('',#265,#270);
#265 = PLANE('',#266);
#266 = AXIS2_PLACEMENT_3D('',#267,#268,#269);
#267 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,4.));
#268 = DIRECTION('',(0.,0.,1.));
#269 = DIRECTION('',(1.,0.,0.));
#270 = DEFINITIONAL_REPRESENTATION('',(#271),#275);
#271 = CIRCLE('',#272,1.165);
#272 = AXIS2_PLACEMENT_2D('',#273,#274);
#273 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#274 = DIRECTION('',(1.,0.));
#275 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#276 = ADVANCED_FACE('',(#277),#265,.F.);
#277 = FACE_BOUND('',#278,.F.);
#278 = EDGE_LOOP('',(#279));
#279 = ORIENTED_EDGE('',*,*,#250,.T.);
#280 = ADVANCED_FACE('',(#281),#237,.T.);
#281 = FACE_BOUND('',#282,.T.);
#282 = EDGE_LOOP('',(#283));
#283 = ORIENTED_EDGE('',*,*,#222,.T.);
#284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#288)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#285,#286,#287)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#285 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#286 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#287 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#288 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#285,
  'distance_accuracy_value','confusion accuracy');
#289 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#290,#292);
#290 = ( REPRESENTATION_RELATIONSHIP('','',#185,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#291) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#291 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#292 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#293
  );
#293 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('2','electrode_001','',#5,#180,$);
#294 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#182));
#295 = SHAPE_DEFINITION_REPRESENTATION(#296,#302);
#296 = PRODUCT_DEFINITION_SHAPE('','',#297);
#297 = PRODUCT_DEFINITION('design','',#298,#301);
#298 = PRODUCT_DEFINITION_FORMATION('','',#299);
#299 = PRODUCT('link_002','link_002','',(#300));
#300 = PRODUCT_CONTEXT('',#2,'mechanical');
#301 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#302 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#303),#401);
#303 = MANIFOLD_SOLID_BREP('',#304);
#304 = CLOSED_SHELL('',(#305,#393,#397));
#305 = ADVANCED_FACE('',(#306),#320,.T.);
#306 = FACE_BOUND('',#307,.F.);
#307 = EDGE_LOOP('',(#308,#338,#365,#366));
#308 = ORIENTED_EDGE('',*,*,#309,.T.);
#309 = EDGE_CURVE('',#310,#312,#314,.T.);
#310 = VERTEX_POINT('',#311);
#311 = CARTESIAN_POINT('',(1.165,0.,7.));
#312 = VERTEX_POINT('',#313);
#313 = CARTESIAN_POINT('',(1.165,0.,12.));
#314 = SEAM_CURVE('',#315,(#319,#331),.PCURVE_S1.);
#315 = LINE('',#316,#317);
#316 = CARTESIAN_POINT('',(1.165,0.,7.));
#317 = VECTOR('',#318,1.);
#318 = DIRECTION('',(0.,0.,1.));
#319 = PCURVE('',#320,#325);
#320 = CYLINDRICAL_SURFACE('',#321,1.165);
#321 = AXIS2_PLACEMENT_3D('',#322,#323,#324);
#322 = CARTESIAN_POINT('',(0.,0.,7.));
#323 = DIRECTION('',(-0.,-0.,-1.));
#324 = DIRECTION('',(1.,0.,0.));
#325 = DEFINITIONAL_REPRESENTATION('',(#326),#330);
#326 = LINE('',#327,#328);
#327 = CARTESIAN_POINT('',(-0.,0.));
#328 = VECTOR('',#329,1.);
#329 = DIRECTION('',(-0.,-1.));
#330 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#331 = PCURVE('',#320,#332);
#332 = DEFINITIONAL_REPRESENTATION('',(#333),#337);
#333 = LINE('',#334,#335);
#334 = CARTESIAN_POINT('',(-6.28318530718,0.));
#335 = VECTOR('',#336,1.);
#336 = DIRECTION('',(-0.,-1.));
#337 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#338 = ORIENTED_EDGE('',*,*,#339,.T.);
#339 = EDGE_CURVE('',#312,#312,#340,.T.);
#340 = SURFACE_CURVE('',#341,(#346,#353),.PCURVE_S1.);
#341 = CIRCLE('',#342,1.165);
#342 = AXIS2_PLACEMENT_3D('',#343,#344,#345);
#343 = CARTESIAN_POINT('',(0.,0.,12.));
#344 = DIRECTION('',(0.,0.,1.));
#345 = DIRECTION('',(1.,0.,0.));
#346 = PCURVE('',#320,#347);
#347 = DEFINITIONAL_REPRESENTATION('',(#348),#352);
#348 = LINE('',#349,#350);
#349 = CARTESIAN_POINT('',(-0.,-5.));
#350 = VECTOR('',#351,1.);
#351 = DIRECTION('',(-1.,0.));
#352 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#353 = PCURVE('',#354,#359);
#354 = PLANE('',#355);
#355 = AXIS2_PLACEMENT_3D('',#356,#357,#358);
#356 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,12.));
#357 = DIRECTION('',(0.,0.,1.));
#358 = DIRECTION('',(1.,0.,0.));
#359 = DEFINITIONAL_REPRESENTATION('',(#360),#364);
#360 = CIRCLE('',#361,1.165);
#361 = AXIS2_PLACEMENT_2D('',#362,#363);
#362 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#363 = DIRECTION('',(1.,0.));
#364 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#365 = ORIENTED_EDGE('',*,*,#309,.F.);
#366 = ORIENTED_EDGE('',*,*,#367,.F.);
#367 = EDGE_CURVE('',#310,#310,#368,.T.);
#368 = SURFACE_CURVE('',#369,(#374,#381),.PCURVE_S1.);
#369 = CIRCLE('',#370,1.165);
#370 = AXIS2_PLACEMENT_3D('',#371,#372,#373);
#371 = CARTESIAN_POINT('',(0.,0.,7.));
#372 = DIRECTION('',(0.,0.,1.));
#373 = DIRECTION('',(1.,0.,0.));
#374 = PCURVE('',#320,#375);
#375 = DEFINITIONAL_REPRESENTATION('',(#376),#380);
#376 = LINE('',#377,#378);
#377 = CARTESIAN_POINT('',(-0.,0.));
#378 = VECTOR('',#379,1.);
#379 = DIRECTION('',(-1.,0.));
#380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#381 = PCURVE('',#382,#387);
#382 = PLANE('',#383);
#383 = AXIS2_PLACEMENT_3D('',#384,#385,#386);
#384 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,7.));
#385 = DIRECTION('',(0.,0.,1.));
#386 = DIRECTION('',(1.,0.,0.));
#387 = DEFINITIONAL_REPRESENTATION('',(#388),#392);
#388 = CIRCLE('',#389,1.165);
#389 = AXIS2_PLACEMENT_2D('',#390,#391);
#390 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#391 = DIRECTION('',(1.,0.));
#392 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#393 = ADVANCED_FACE('',(#394),#382,.F.);
#394 = FACE_BOUND('',#395,.F.);
#395 = EDGE_LOOP('',(#396));
#396 = ORIENTED_EDGE('',*,*,#367,.T.);
#397 = ADVANCED_FACE('',(#398),#354,.T.);
#398 = FACE_BOUND('',#399,.T.);
#399 = EDGE_LOOP('',(#400));
#400 = ORIENTED_EDGE('',*,*,#339,.T.);
#401 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#405)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#402,#403,#404)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#402 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#403 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#404 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#405 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#402,
  'distance_accuracy_value','confusion accuracy');
#406 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#407,#409);
#407 = ( REPRESENTATION_RELATIONSHIP('','',#302,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#408) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#408 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#409 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#410
  );
#410 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('3','link_002','',#5,#297,$);
#411 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#299));
#412 = SHAPE_DEFINITION_REPRESENTATION(#413,#419);
#413 = PRODUCT_DEFINITION_SHAPE('','',#414);
#414 = PRODUCT_DEFINITION('design','',#415,#418);
#415 = PRODUCT_DEFINITION_FORMATION('','',#416);
#416 = PRODUCT('electrode_002','electrode_002','',(#417));
#417 = PRODUCT_CONTEXT('',#2,'mechanical');
#418 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#419 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#420),#518);
#420 = MANIFOLD_SOLID_BREP('',#421);
#421 = CLOSED_SHELL('',(#422,#510,#514));
#422 = ADVANCED_FACE('',(#423),#437,.T.);
#423 = FACE_BOUND('',#424,.F.);
#424 = EDGE_LOOP('',(#425,#455,#482,#483));
#425 = ORIENTED_EDGE('',*,*,#426,.T.);
#426 = EDGE_CURVE('',#427,#429,#431,.T.);
#427 = VERTEX_POINT('',#428);
#428 = CARTESIAN_POINT('',(1.165,0.,10.));
#429 = VERTEX_POINT('',#430);
#430 = CARTESIAN_POINT('',(1.165,0.,11.));
#431 = SEAM_CURVE('',#432,(#436,#448),.PCURVE_S1.);
#432 = LINE('',#433,#434);
#433 = CARTESIAN_POINT('',(1.165,0.,10.));
#434 = VECTOR('',#435,1.);
#435 = DIRECTION('',(0.,0.,1.));
#436 = PCURVE('',#437,#442);
#437 = CYLINDRICAL_SURFACE('',#438,1.165);
#438 = AXIS2_PLACEMENT_3D('',#439,#440,#441);
#439 = CARTESIAN_POINT('',(0.,0.,10.));
#440 = DIRECTION('',(-0.,-0.,-1.));
#441 = DIRECTION('',(1.,0.,0.));
#442 = DEFINITIONAL_REPRESENTATION('',(#443),#447);
#443 = LINE('',#444,#445);
#444 = CARTESIAN_POINT('',(-0.,0.));
#445 = VECTOR('',#446,1.);
#446 = DIRECTION('',(-0.,-1.));
#447 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#448 = PCURVE('',#437,#449);
#449 = DEFINITIONAL_REPRESENTATION('',(#450),#454);
#450 = LINE('',#451,#452);
#451 = CARTESIAN_POINT('',(-6.28318530718,0.));
#452 = VECTOR('',#453,1.);
#453 = DIRECTION('',(-0.,-1.));
#454 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#455 = ORIENTED_EDGE('',*,*,#456,.T.);
#456 = EDGE_CURVE('',#429,#429,#457,.T.);
#457 = SURFACE_CURVE('',#458,(#463,#470),.PCURVE_S1.);
#458 = CIRCLE('',#459,1.165);
#459 = AXIS2_PLACEMENT_3D('',#460,#461,#462);
#460 = CARTESIAN_POINT('',(0.,0.,11.));
#461 = DIRECTION('',(0.,0.,1.));
#462 = DIRECTION('',(1.,0.,0.));
#463 = PCURVE('',#437,#464);
#464 = DEFINITIONAL_REPRESENTATION('',(#465),#469);
#465 = LINE('',#466,#467);
#466 = CARTESIAN_POINT('',(-0.,-1.));
#467 = VECTOR('',#468,1.);
#468 = DIRECTION('',(-1.,0.));
#469 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#470 = PCURVE('',#471,#476);
#471 = PLANE('',#472);
#472 = AXIS2_PLACEMENT_3D('',#473,#474,#475);
#473 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,11.));
#474 = DIRECTION('',(0.,0.,1.));
#475 = DIRECTION('',(1.,0.,0.));
#476 = DEFINITIONAL_REPRESENTATION('',(#477),#481);
#477 = CIRCLE('',#478,1.165);
#478 = AXIS2_PLACEMENT_2D('',#479,#480);
#479 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#480 = DIRECTION('',(1.,0.));
#481 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#482 = ORIENTED_EDGE('',*,*,#426,.F.);
#483 = ORIENTED_EDGE('',*,*,#484,.F.);
#484 = EDGE_CURVE('',#427,#427,#485,.T.);
#485 = SURFACE_CURVE('',#486,(#491,#498),.PCURVE_S1.);
#486 = CIRCLE('',#487,1.165);
#487 = AXIS2_PLACEMENT_3D('',#488,#489,#490);
#488 = CARTESIAN_POINT('',(0.,0.,10.));
#489 = DIRECTION('',(0.,0.,1.));
#490 = DIRECTION('',(1.,0.,0.));
#491 = PCURVE('',#437,#492);
#492 = DEFINITIONAL_REPRESENTATION('',(#493),#497);
#493 = LINE('',#494,#495);
#494 = CARTESIAN_POINT('',(-0.,0.));
#495 = VECTOR('',#496,1.);
#496 = DIRECTION('',(-1.,0.));
#497 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#498 = PCURVE('',#499,#504);
#499 = PLANE('',#500);
#500 = AXIS2_PLACEMENT_3D('',#501,#502,#503);
#501 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,10.));
#502 = DIRECTION('',(0.,0.,1.));
#503 = DIRECTION('',(1.,0.,0.));
#504 = DEFINITIONAL_REPRESENTATION('',(#505),#509);
#505 = CIRCLE('',#506,1.165);
#506 = AXIS2_PLACEMENT_2D('',#507,#508);
#507 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#508 = DIRECTION('',(1.,0.));
#509 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#510 = ADVANCED_FACE('',(#511),#499,.F.);
#511 = FACE_BOUND('',#512,.F.);
#512 = EDGE_LOOP('',(#513));
#513 = ORIENTED_EDGE('',*,*,#484,.T.);
#514 = ADVANCED_FACE('',(#515),#471,.T.);
#515 = FACE_BOUND('',#516,.T.);
#516 = EDGE_LOOP('',(#517));
#517 = ORIENTED_EDGE('',*,*,#456,.T.);
#518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#522)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#519,#520,#521)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#519 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#520 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#521 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#522 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#519,
  'distance_accuracy_value','confusion accuracy');
#523 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#524,#526);
#524 = ( REPRESENTATION_RELATIONSHIP('','',#419,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#525) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#525 = ITEM_DEFINED_TRANSFORMATION('','',#11,#27);
#526 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#527
  );
#527 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('4','electrode_002','',#5,#414,$);
#528 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#416));
#529 = SHAPE_DEFINITION_REPRESENTATION(#530,#536);
#530 = PRODUCT_DEFINITION_SHAPE('','',#531);
#531 = PRODUCT_DEFINITION('design','',#532,#535);
#532 = PRODUCT_DEFINITION_FORMATION('','',#533);
#533 = PRODUCT('link_003','link_003','',(#534));
#534 = PRODUCT_CONTEXT('',#2,'mechanical');
#535 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#536 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#537),#635);
#537 = MANIFOLD_SOLID_BREP('',#538);
#538 = CLOSED_SHELL('',(#539,#627,#631));
#539 = ADVANCED_FACE('',(#540),#554,.T.);
#540 = FACE_BOUND('',#541,.F.);
#541 = EDGE_LOOP('',(#542,#572,#599,#600));
#542 = ORIENTED_EDGE('',*,*,#543,.T.);
#543 = EDGE_CURVE('',#544,#546,#548,.T.);
#544 = VERTEX_POINT('',#545);
#545 = CARTESIAN_POINT('',(1.165,0.,13.));
#546 = VERTEX_POINT('',#547);
#547 = CARTESIAN_POINT('',(1.165,0.,15.));
#548 = SEAM_CURVE('',#549,(#553,#565),.PCURVE_S1.);
#549 = LINE('',#550,#551);
#550 = CARTESIAN_POINT('',(1.165,0.,13.));
#551 = VECTOR('',#552,1.);
#552 = DIRECTION('',(0.,0.,1.));
#553 = PCURVE('',#554,#559);
#554 = CYLINDRICAL_SURFACE('',#555,1.165);
#555 = AXIS2_PLACEMENT_3D('',#556,#557,#558);
#556 = CARTESIAN_POINT('',(0.,0.,13.));
#557 = DIRECTION('',(-0.,-0.,-1.));
#558 = DIRECTION('',(1.,0.,0.));
#559 = DEFINITIONAL_REPRESENTATION('',(#560),#564);
#560 = LINE('',#561,#562);
#561 = CARTESIAN_POINT('',(-0.,0.));
#562 = VECTOR('',#563,1.);
#563 = DIRECTION('',(-0.,-1.));
#564 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#565 = PCURVE('',#554,#566);
#566 = DEFINITIONAL_REPRESENTATION('',(#567),#571);
#567 = LINE('',#568,#569);
#568 = CARTESIAN_POINT('',(-6.28318530718,0.));
#569 = VECTOR('',#570,1.);
#570 = DIRECTION('',(-0.,-1.));
#571 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#572 = ORIENTED_EDGE('',*,*,#573,.T.);
#573 = EDGE_CURVE('',#546,#546,#574,.T.);
#574 = SURFACE_CURVE('',#575,(#580,#587),.PCURVE_S1.);
#575 = CIRCLE('',#576,1.165);
#576 = AXIS2_PLACEMENT_3D('',#577,#578,#579);
#577 = CARTESIAN_POINT('',(0.,0.,15.));
#578 = DIRECTION('',(0.,0.,1.));
#579 = DIRECTION('',(1.,0.,0.));
#580 = PCURVE('',#554,#581);
#581 = DEFINITIONAL_REPRESENTATION('',(#582),#586);
#582 = LINE('',#583,#584);
#583 = CARTESIAN_POINT('',(-0.,-2.));
#584 = VECTOR('',#585,1.);
#585 = DIRECTION('',(-1.,0.));
#586 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#587 = PCURVE('',#588,#593);
#588 = PLANE('',#589);
#589 = AXIS2_PLACEMENT_3D('',#590,#591,#592);
#590 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,15.));
#591 = DIRECTION('',(0.,0.,1.));
#592 = DIRECTION('',(1.,0.,0.));
#593 = DEFINITIONAL_REPRESENTATION('',(#594),#598);
#594 = CIRCLE('',#595,1.165);
#595 = AXIS2_PLACEMENT_2D('',#596,#597);
#596 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#597 = DIRECTION('',(1.,0.));
#598 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#599 = ORIENTED_EDGE('',*,*,#543,.F.);
#600 = ORIENTED_EDGE('',*,*,#601,.F.);
#601 = EDGE_CURVE('',#544,#544,#602,.T.);
#602 = SURFACE_CURVE('',#603,(#608,#615),.PCURVE_S1.);
#603 = CIRCLE('',#604,1.165);
#604 = AXIS2_PLACEMENT_3D('',#605,#606,#607);
#605 = CARTESIAN_POINT('',(0.,0.,13.));
#606 = DIRECTION('',(0.,0.,1.));
#607 = DIRECTION('',(1.,0.,0.));
#608 = PCURVE('',#554,#609);
#609 = DEFINITIONAL_REPRESENTATION('',(#610),#614);
#610 = LINE('',#611,#612);
#611 = CARTESIAN_POINT('',(-0.,0.));
#612 = VECTOR('',#613,1.);
#613 = DIRECTION('',(-1.,0.));
#614 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#615 = PCURVE('',#616,#621);
#616 = PLANE('',#617);
#617 = AXIS2_PLACEMENT_3D('',#618,#619,#620);
#618 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,13.));
#619 = DIRECTION('',(0.,0.,1.));
#620 = DIRECTION('',(1.,0.,0.));
#621 = DEFINITIONAL_REPRESENTATION('',(#622),#626);
#622 = CIRCLE('',#623,1.165);
#623 = AXIS2_PLACEMENT_2D('',#624,#625);
#624 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#625 = DIRECTION('',(1.,0.));
#626 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#627 = ADVANCED_FACE('',(#628),#616,.F.);
#628 = FACE_BOUND('',#629,.F.);
#629 = EDGE_LOOP('',(#630));
#630 = ORIENTED_EDGE('',*,*,#601,.T.);
#631 = ADVANCED_FACE('',(#632),#588,.T.);
#632 = FACE_BOUND('',#633,.T.);
#633 = EDGE_LOOP('',(#634));
#634 = ORIENTED_EDGE('',*,*,#573,.T.);
#635 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#639)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#636,#637,#638)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#636 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#637 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#638 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#639 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#636,
  'distance_accuracy_value','confusion accuracy');
#640 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#641,#643);
#641 = ( REPRESENTATION_RELATIONSHIP('','',#536,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#642) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#642 = ITEM_DEFINED_TRANSFORMATION('','',#11,#31);
#643 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#644
  );
#644 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('5','link_003','',#5,#531,$);
#645 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#533));
#646 = SHAPE_DEFINITION_REPRESENTATION(#647,#653);
#647 = PRODUCT_DEFINITION_SHAPE('','',#648);
#648 = PRODUCT_DEFINITION('design','',#649,#652);
#649 = PRODUCT_DEFINITION_FORMATION('','',#650);
#650 = PRODUCT('electrode_003','electrode_003','',(#651));
#651 = PRODUCT_CONTEXT('',#2,'mechanical');
#652 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#653 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#654),#752);
#654 = MANIFOLD_SOLID_BREP('',#655);
#655 = CLOSED_SHELL('',(#656,#744,#748));
#656 = ADVANCED_FACE('',(#657),#671,.T.);
#657 = FACE_BOUND('',#658,.F.);
#658 = EDGE_LOOP('',(#659,#689,#716,#717));
#659 = ORIENTED_EDGE('',*,*,#660,.T.);
#660 = EDGE_CURVE('',#661,#663,#665,.T.);
#661 = VERTEX_POINT('',#662);
#662 = CARTESIAN_POINT('',(1.165,0.,13.));
#663 = VERTEX_POINT('',#664);
#664 = CARTESIAN_POINT('',(1.165,0.,14.));
#665 = SEAM_CURVE('',#666,(#670,#682),.PCURVE_S1.);
#666 = LINE('',#667,#668);
#667 = CARTESIAN_POINT('',(1.165,0.,13.));
#668 = VECTOR('',#669,1.);
#669 = DIRECTION('',(0.,0.,1.));
#670 = PCURVE('',#671,#676);
#671 = CYLINDRICAL_SURFACE('',#672,1.165);
#672 = AXIS2_PLACEMENT_3D('',#673,#674,#675);
#673 = CARTESIAN_POINT('',(0.,0.,13.));
#674 = DIRECTION('',(-0.,-0.,-1.));
#675 = DIRECTION('',(1.,0.,0.));
#676 = DEFINITIONAL_REPRESENTATION('',(#677),#681);
#677 = LINE('',#678,#679);
#678 = CARTESIAN_POINT('',(-0.,0.));
#679 = VECTOR('',#680,1.);
#680 = DIRECTION('',(-0.,-1.));
#681 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#682 = PCURVE('',#671,#683);
#683 = DEFINITIONAL_REPRESENTATION('',(#684),#688);
#684 = LINE('',#685,#686);
#685 = CARTESIAN_POINT('',(-6.28318530718,0.));
#686 = VECTOR('',#687,1.);
#687 = DIRECTION('',(-0.,-1.));
#688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#689 = ORIENTED_EDGE('',*,*,#690,.T.);
#690 = EDGE_CURVE('',#663,#663,#691,.T.);
#691 = SURFACE_CURVE('',#692,(#697,#704),.PCURVE_S1.);
#692 = CIRCLE('',#693,1.165);
#693 = AXIS2_PLACEMENT_3D('',#694,#695,#696);
#694 = CARTESIAN_POINT('',(0.,0.,14.));
#695 = DIRECTION('',(0.,0.,1.));
#696 = DIRECTION('',(1.,0.,0.));
#697 = PCURVE('',#671,#698);
#698 = DEFINITIONAL_REPRESENTATION('',(#699),#703);
#699 = LINE('',#700,#701);
#700 = CARTESIAN_POINT('',(-0.,-1.));
#701 = VECTOR('',#702,1.);
#702 = DIRECTION('',(-1.,0.));
#703 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#704 = PCURVE('',#705,#710);
#705 = PLANE('',#706);
#706 = AXIS2_PLACEMENT_3D('',#707,#708,#709);
#707 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,14.));
#708 = DIRECTION('',(0.,0.,1.));
#709 = DIRECTION('',(1.,0.,0.));
#710 = DEFINITIONAL_REPRESENTATION('',(#711),#715);
#711 = CIRCLE('',#712,1.165);
#712 = AXIS2_PLACEMENT_2D('',#713,#714);
#713 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#714 = DIRECTION('',(1.,0.));
#715 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#716 = ORIENTED_EDGE('',*,*,#660,.F.);
#717 = ORIENTED_EDGE('',*,*,#718,.F.);
#718 = EDGE_CURVE('',#661,#661,#719,.T.);
#719 = SURFACE_CURVE('',#720,(#725,#732),.PCURVE_S1.);
#720 = CIRCLE('',#721,1.165);
#721 = AXIS2_PLACEMENT_3D('',#722,#723,#724);
#722 = CARTESIAN_POINT('',(0.,0.,13.));
#723 = DIRECTION('',(0.,0.,1.));
#724 = DIRECTION('',(1.,0.,0.));
#725 = PCURVE('',#671,#726);
#726 = DEFINITIONAL_REPRESENTATION('',(#727),#731);
#727 = LINE('',#728,#729);
#728 = CARTESIAN_POINT('',(-0.,0.));
#729 = VECTOR('',#730,1.);
#730 = DIRECTION('',(-1.,0.));
#731 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#732 = PCURVE('',#733,#738);
#733 = PLANE('',#734);
#734 = AXIS2_PLACEMENT_3D('',#735,#736,#737);
#735 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,13.));
#736 = DIRECTION('',(0.,0.,1.));
#737 = DIRECTION('',(1.,0.,0.));
#738 = DEFINITIONAL_REPRESENTATION('',(#739),#743);
#739 = CIRCLE('',#740,1.165);
#740 = AXIS2_PLACEMENT_2D('',#741,#742);
#741 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#742 = DIRECTION('',(1.,0.));
#743 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#744 = ADVANCED_FACE('',(#745),#733,.F.);
#745 = FACE_BOUND('',#746,.F.);
#746 = EDGE_LOOP('',(#747));
#747 = ORIENTED_EDGE('',*,*,#718,.T.);
#748 = ADVANCED_FACE('',(#749),#705,.T.);
#749 = FACE_BOUND('',#750,.T.);
#750 = EDGE_LOOP('',(#751));
#751 = ORIENTED_EDGE('',*,*,#690,.T.);
#752 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#756)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#753,#754,#755)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#753 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#754 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#755 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#756 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#753,
  'distance_accuracy_value','confusion accuracy');
#757 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#758,#760);
#758 = ( REPRESENTATION_RELATIONSHIP('','',#653,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#759) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#759 = ITEM_DEFINED_TRANSFORMATION('','',#11,#35);
#760 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#761
  );
#761 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('6','electrode_003','',#5,#648,$);
#762 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#650));
#763 = SHAPE_DEFINITION_REPRESENTATION(#764,#770);
#764 = PRODUCT_DEFINITION_SHAPE('','',#765);
#765 = PRODUCT_DEFINITION('design','',#766,#769);
#766 = PRODUCT_DEFINITION_FORMATION('','',#767);
#767 = PRODUCT('link_004','link_004','',(#768));
#768 = PRODUCT_CONTEXT('',#2,'mechanical');
#769 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#770 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#771),#869);
#771 = MANIFOLD_SOLID_BREP('',#772);
#772 = CLOSED_SHELL('',(#773,#861,#865));
#773 = ADVANCED_FACE('',(#774),#788,.T.);
#774 = FACE_BOUND('',#775,.F.);
#775 = EDGE_LOOP('',(#776,#806,#833,#834));
#776 = ORIENTED_EDGE('',*,*,#777,.T.);
#777 = EDGE_CURVE('',#778,#780,#782,.T.);
#778 = VERTEX_POINT('',#779);
#779 = CARTESIAN_POINT('',(1.165,0.,16.));
#780 = VERTEX_POINT('',#781);
#781 = CARTESIAN_POINT('',(1.165,0.,18.));
#782 = SEAM_CURVE('',#783,(#787,#799),.PCURVE_S1.);
#783 = LINE('',#784,#785);
#784 = CARTESIAN_POINT('',(1.165,0.,16.));
#785 = VECTOR('',#786,1.);
#786 = DIRECTION('',(0.,0.,1.));
#787 = PCURVE('',#788,#793);
#788 = CYLINDRICAL_SURFACE('',#789,1.165);
#789 = AXIS2_PLACEMENT_3D('',#790,#791,#792);
#790 = CARTESIAN_POINT('',(0.,0.,16.));
#791 = DIRECTION('',(-0.,-0.,-1.));
#792 = DIRECTION('',(1.,0.,0.));
#793 = DEFINITIONAL_REPRESENTATION('',(#794),#798);
#794 = LINE('',#795,#796);
#795 = CARTESIAN_POINT('',(-0.,0.));
#796 = VECTOR('',#797,1.);
#797 = DIRECTION('',(-0.,-1.));
#798 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#799 = PCURVE('',#788,#800);
#800 = DEFINITIONAL_REPRESENTATION('',(#801),#805);
#801 = LINE('',#802,#803);
#802 = CARTESIAN_POINT('',(-6.28318530718,0.));
#803 = VECTOR('',#804,1.);
#804 = DIRECTION('',(-0.,-1.));
#805 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#806 = ORIENTED_EDGE('',*,*,#807,.T.);
#807 = EDGE_CURVE('',#780,#780,#808,.T.);
#808 = SURFACE_CURVE('',#809,(#814,#821),.PCURVE_S1.);
#809 = CIRCLE('',#810,1.165);
#810 = AXIS2_PLACEMENT_3D('',#811,#812,#813);
#811 = CARTESIAN_POINT('',(0.,0.,18.));
#812 = DIRECTION('',(0.,0.,1.));
#813 = DIRECTION('',(1.,0.,0.));
#814 = PCURVE('',#788,#815);
#815 = DEFINITIONAL_REPRESENTATION('',(#816),#820);
#816 = LINE('',#817,#818);
#817 = CARTESIAN_POINT('',(-0.,-2.));
#818 = VECTOR('',#819,1.);
#819 = DIRECTION('',(-1.,0.));
#820 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#821 = PCURVE('',#822,#827);
#822 = PLANE('',#823);
#823 = AXIS2_PLACEMENT_3D('',#824,#825,#826);
#824 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,18.));
#825 = DIRECTION('',(0.,0.,1.));
#826 = DIRECTION('',(1.,0.,0.));
#827 = DEFINITIONAL_REPRESENTATION('',(#828),#832);
#828 = CIRCLE('',#829,1.165);
#829 = AXIS2_PLACEMENT_2D('',#830,#831);
#830 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#831 = DIRECTION('',(1.,0.));
#832 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#833 = ORIENTED_EDGE('',*,*,#777,.F.);
#834 = ORIENTED_EDGE('',*,*,#835,.F.);
#835 = EDGE_CURVE('',#778,#778,#836,.T.);
#836 = SURFACE_CURVE('',#837,(#842,#849),.PCURVE_S1.);
#837 = CIRCLE('',#838,1.165);
#838 = AXIS2_PLACEMENT_3D('',#839,#840,#841);
#839 = CARTESIAN_POINT('',(0.,0.,16.));
#840 = DIRECTION('',(0.,0.,1.));
#841 = DIRECTION('',(1.,0.,0.));
#842 = PCURVE('',#788,#843);
#843 = DEFINITIONAL_REPRESENTATION('',(#844),#848);
#844 = LINE('',#845,#846);
#845 = CARTESIAN_POINT('',(-0.,0.));
#846 = VECTOR('',#847,1.);
#847 = DIRECTION('',(-1.,0.));
#848 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#849 = PCURVE('',#850,#855);
#850 = PLANE('',#851);
#851 = AXIS2_PLACEMENT_3D('',#852,#853,#854);
#852 = CARTESIAN_POINT('',(-1.1E-16,3.E-17,16.));
#853 = DIRECTION('',(0.,0.,1.));
#854 = DIRECTION('',(1.,0.,0.));
#855 = DEFINITIONAL_REPRESENTATION('',(#856),#860);
#856 = CIRCLE('',#857,1.165);
#857 = AXIS2_PLACEMENT_2D('',#858,#859);
#858 = CARTESIAN_POINT('',(1.1E-16,-3.E-17));
#859 = DIRECTION('',(1.,0.));
#860 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#861 = ADVANCED_FACE('',(#862),#850,.F.);
#862 = FACE_BOUND('',#863,.F.);
#863 = EDGE_LOOP('',(#864));
#864 = ORIENTED_EDGE('',*,*,#835,.T.);
#865 = ADVANCED_FACE('',(#866),#822,.T.);
#866 = FACE_BOUND('',#867,.T.);
#867 = EDGE_LOOP('',(#868));
#868 = ORIENTED_EDGE('',*,*,#807,.T.);
#869 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#873)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#870,#871,#872)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#870 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#871 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#872 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#873 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#870,
  'distance_accuracy_value','confusion accuracy');
#874 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#875,#877);
#875 = ( REPRESENTATION_RELATIONSHIP('','',#770,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#876) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#876 = ITEM_DEFINED_TRANSFORMATION('','',#11,#39);
#877 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#878
  );
#878 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('7','link_004','',#5,#765,$);
#879 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#767));
#880 = SHAPE_DEFINITION_REPRESENTATION(#881,#887);
#881 = PRODUCT_DEFINITION_SHAPE('','',#882);
#882 = PRODUCT_DEFINITION('design','',#883,#886);
#883 = PRODUCT_DEFINITION_FORMATION('','',#884);
#884 = PRODUCT('圆柱体','圆柱体','',(#885));
#885 = PRODUCT_CONTEXT('',#2,'mechanical');
#886 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#887 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#888),#1142);
#888 = MANIFOLD_SOLID_BREP('',#889);
#889 = CLOSED_SHELL('',(#890,#1012,#1083,#1130,#1136));
#890 = ADVANCED_FACE('',(#891),#905,.T.);
#891 = FACE_BOUND('',#892,.F.);
#892 = EDGE_LOOP('',(#893,#928,#957,#985));
#893 = ORIENTED_EDGE('',*,*,#894,.F.);
#894 = EDGE_CURVE('',#895,#897,#899,.T.);
#895 = VERTEX_POINT('',#896);
#896 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#897 = VERTEX_POINT('',#898);
#898 = CARTESIAN_POINT('',(-0.575,0.995929214352,4.));
#899 = SURFACE_CURVE('',#900,(#904,#916),.PCURVE_S1.);
#900 = LINE('',#901,#902);
#901 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#902 = VECTOR('',#903,1.);
#903 = DIRECTION('',(0.,0.,1.));
#904 = PCURVE('',#905,#910);
#905 = CYLINDRICAL_SURFACE('',#906,1.15);
#906 = AXIS2_PLACEMENT_3D('',#907,#908,#909);
#907 = CARTESIAN_POINT('',(0.,0.,0.));
#908 = DIRECTION('',(-0.,-0.,-1.));
#909 = DIRECTION('',(1.,0.,-0.));
#910 = DEFINITIONAL_REPRESENTATION('',(#911),#915);
#911 = LINE('',#912,#913);
#912 = CARTESIAN_POINT('',(-2.094395102393,0.));
#913 = VECTOR('',#914,1.);
#914 = DIRECTION('',(-0.,-1.));
#915 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#916 = PCURVE('',#917,#922);
#917 = PLANE('',#918);
#918 = AXIS2_PLACEMENT_3D('',#919,#920,#921);
#919 = CARTESIAN_POINT('',(0.,0.,0.));
#920 = DIRECTION('',(-0.866025403784,-0.5,0.));
#921 = DIRECTION('',(-0.5,0.866025403784,0.));
#922 = DEFINITIONAL_REPRESENTATION('',(#923),#927);
#923 = LINE('',#924,#925);
#924 = CARTESIAN_POINT('',(1.15,0.));
#925 = VECTOR('',#926,1.);
#926 = DIRECTION('',(0.,-1.));
#927 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#928 = ORIENTED_EDGE('',*,*,#929,.F.);
#929 = EDGE_CURVE('',#930,#895,#932,.T.);
#930 = VERTEX_POINT('',#931);
#931 = CARTESIAN_POINT('',(1.15,0.,0.));
#932 = SURFACE_CURVE('',#933,(#938,#945),.PCURVE_S1.);
#933 = CIRCLE('',#934,1.15);
#934 = AXIS2_PLACEMENT_3D('',#935,#936,#937);
#935 = CARTESIAN_POINT('',(0.,0.,0.));
#936 = DIRECTION('',(0.,0.,1.));
#937 = DIRECTION('',(1.,0.,-0.));
#938 = PCURVE('',#905,#939);
#939 = DEFINITIONAL_REPRESENTATION('',(#940),#944);
#940 = LINE('',#941,#942);
#941 = CARTESIAN_POINT('',(-0.,0.));
#942 = VECTOR('',#943,1.);
#943 = DIRECTION('',(-1.,0.));
#944 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#945 = PCURVE('',#946,#951);
#946 = PLANE('',#947);
#947 = AXIS2_PLACEMENT_3D('',#948,#949,#950);
#948 = CARTESIAN_POINT('',(0.,0.,0.));
#949 = DIRECTION('',(0.,0.,1.));
#950 = DIRECTION('',(1.,0.,-0.));
#951 = DEFINITIONAL_REPRESENTATION('',(#952),#956);
#952 = CIRCLE('',#953,1.15);
#953 = AXIS2_PLACEMENT_2D('',#954,#955);
#954 = CARTESIAN_POINT('',(0.,0.));
#955 = DIRECTION('',(1.,0.));
#956 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#957 = ORIENTED_EDGE('',*,*,#958,.T.);
#958 = EDGE_CURVE('',#930,#959,#961,.T.);
#959 = VERTEX_POINT('',#960);
#960 = CARTESIAN_POINT('',(1.15,0.,4.));
#961 = SURFACE_CURVE('',#962,(#966,#973),.PCURVE_S1.);
#962 = LINE('',#963,#964);
#963 = CARTESIAN_POINT('',(1.15,0.,0.));
#964 = VECTOR('',#965,1.);
#965 = DIRECTION('',(0.,0.,1.));
#966 = PCURVE('',#905,#967);
#967 = DEFINITIONAL_REPRESENTATION('',(#968),#972);
#968 = LINE('',#969,#970);
#969 = CARTESIAN_POINT('',(-0.,0.));
#970 = VECTOR('',#971,1.);
#971 = DIRECTION('',(-0.,-1.));
#972 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#973 = PCURVE('',#974,#979);
#974 = PLANE('',#975);
#975 = AXIS2_PLACEMENT_3D('',#976,#977,#978);
#976 = CARTESIAN_POINT('',(0.,0.,0.));
#977 = DIRECTION('',(-0.,1.,0.));
#978 = DIRECTION('',(1.,0.,-0.));
#979 = DEFINITIONAL_REPRESENTATION('',(#980),#984);
#980 = LINE('',#981,#982);
#981 = CARTESIAN_POINT('',(1.15,0.));
#982 = VECTOR('',#983,1.);
#983 = DIRECTION('',(0.,-1.));
#984 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#985 = ORIENTED_EDGE('',*,*,#986,.T.);
#986 = EDGE_CURVE('',#959,#897,#987,.T.);
#987 = SURFACE_CURVE('',#988,(#993,#1000),.PCURVE_S1.);
#988 = CIRCLE('',#989,1.15);
#989 = AXIS2_PLACEMENT_3D('',#990,#991,#992);
#990 = CARTESIAN_POINT('',(0.,0.,4.));
#991 = DIRECTION('',(0.,0.,1.));
#992 = DIRECTION('',(1.,0.,0.));
#993 = PCURVE('',#905,#994);
#994 = DEFINITIONAL_REPRESENTATION('',(#995),#999);
#995 = LINE('',#996,#997);
#996 = CARTESIAN_POINT('',(-0.,-4.));
#997 = VECTOR('',#998,1.);
#998 = DIRECTION('',(-1.,0.));
#999 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1000 = PCURVE('',#1001,#1006);
#1001 = PLANE('',#1002);
#1002 = AXIS2_PLACEMENT_3D('',#1003,#1004,#1005);
#1003 = CARTESIAN_POINT('',(0.,0.,4.));
#1004 = DIRECTION('',(0.,0.,1.));
#1005 = DIRECTION('',(1.,0.,0.));
#1006 = DEFINITIONAL_REPRESENTATION('',(#1007),#1011);
#1007 = CIRCLE('',#1008,1.15);
#1008 = AXIS2_PLACEMENT_2D('',#1009,#1010);
#1009 = CARTESIAN_POINT('',(0.,0.));
#1010 = DIRECTION('',(1.,0.));
#1011 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1012 = ADVANCED_FACE('',(#1013),#917,.T.);
#1013 = FACE_BOUND('',#1014,.T.);
#1014 = EDGE_LOOP('',(#1015,#1040,#1061,#1062));
#1015 = ORIENTED_EDGE('',*,*,#1016,.T.);
#1016 = EDGE_CURVE('',#1017,#1019,#1021,.T.);
#1017 = VERTEX_POINT('',#1018);
#1018 = CARTESIAN_POINT('',(0.,0.,0.));
#1019 = VERTEX_POINT('',#1020);
#1020 = CARTESIAN_POINT('',(0.,0.,4.));
#1021 = SURFACE_CURVE('',#1022,(#1026,#1033),.PCURVE_S1.);
#1022 = LINE('',#1023,#1024);
#1023 = CARTESIAN_POINT('',(0.,0.,0.));
#1024 = VECTOR('',#1025,1.);
#1025 = DIRECTION('',(0.,0.,1.));
#1026 = PCURVE('',#917,#1027);
#1027 = DEFINITIONAL_REPRESENTATION('',(#1028),#1032);
#1028 = LINE('',#1029,#1030);
#1029 = CARTESIAN_POINT('',(0.,0.));
#1030 = VECTOR('',#1031,1.);
#1031 = DIRECTION('',(0.,-1.));
#1032 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1033 = PCURVE('',#974,#1034);
#1034 = DEFINITIONAL_REPRESENTATION('',(#1035),#1039);
#1035 = LINE('',#1036,#1037);
#1036 = CARTESIAN_POINT('',(0.,0.));
#1037 = VECTOR('',#1038,1.);
#1038 = DIRECTION('',(0.,-1.));
#1039 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1040 = ORIENTED_EDGE('',*,*,#1041,.T.);
#1041 = EDGE_CURVE('',#1019,#897,#1042,.T.);
#1042 = SURFACE_CURVE('',#1043,(#1047,#1054),.PCURVE_S1.);
#1043 = LINE('',#1044,#1045);
#1044 = CARTESIAN_POINT('',(0.,0.,4.));
#1045 = VECTOR('',#1046,1.);
#1046 = DIRECTION('',(-0.5,0.866025403784,0.));
#1047 = PCURVE('',#917,#1048);
#1048 = DEFINITIONAL_REPRESENTATION('',(#1049),#1053);
#1049 = LINE('',#1050,#1051);
#1050 = CARTESIAN_POINT('',(0.,-4.));
#1051 = VECTOR('',#1052,1.);
#1052 = DIRECTION('',(1.,0.));
#1053 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1054 = PCURVE('',#1001,#1055);
#1055 = DEFINITIONAL_REPRESENTATION('',(#1056),#1060);
#1056 = LINE('',#1057,#1058);
#1057 = CARTESIAN_POINT('',(0.,0.));
#1058 = VECTOR('',#1059,1.);
#1059 = DIRECTION('',(-0.5,0.866025403784));
#1060 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1061 = ORIENTED_EDGE('',*,*,#894,.F.);
#1062 = ORIENTED_EDGE('',*,*,#1063,.F.);
#1063 = EDGE_CURVE('',#1017,#895,#1064,.T.);
#1064 = SURFACE_CURVE('',#1065,(#1069,#1076),.PCURVE_S1.);
#1065 = LINE('',#1066,#1067);
#1066 = CARTESIAN_POINT('',(0.,0.,0.));
#1067 = VECTOR('',#1068,1.);
#1068 = DIRECTION('',(-0.5,0.866025403784,0.));
#1069 = PCURVE('',#917,#1070);
#1070 = DEFINITIONAL_REPRESENTATION('',(#1071),#1075);
#1071 = LINE('',#1072,#1073);
#1072 = CARTESIAN_POINT('',(0.,0.));
#1073 = VECTOR('',#1074,1.);
#1074 = DIRECTION('',(1.,0.));
#1075 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1076 = PCURVE('',#946,#1077);
#1077 = DEFINITIONAL_REPRESENTATION('',(#1078),#1082);
#1078 = LINE('',#1079,#1080);
#1079 = CARTESIAN_POINT('',(0.,0.));
#1080 = VECTOR('',#1081,1.);
#1081 = DIRECTION('',(-0.5,0.866025403784));
#1082 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1083 = ADVANCED_FACE('',(#1084),#974,.F.);
#1084 = FACE_BOUND('',#1085,.F.);
#1085 = EDGE_LOOP('',(#1086,#1087,#1108,#1109));
#1086 = ORIENTED_EDGE('',*,*,#958,.F.);
#1087 = ORIENTED_EDGE('',*,*,#1088,.F.);
#1088 = EDGE_CURVE('',#1017,#930,#1089,.T.);
#1089 = SURFACE_CURVE('',#1090,(#1094,#1101),.PCURVE_S1.);
#1090 = LINE('',#1091,#1092);
#1091 = CARTESIAN_POINT('',(0.,0.,0.));
#1092 = VECTOR('',#1093,1.);
#1093 = DIRECTION('',(1.,0.,-0.));
#1094 = PCURVE('',#974,#1095);
#1095 = DEFINITIONAL_REPRESENTATION('',(#1096),#1100);
#1096 = LINE('',#1097,#1098);
#1097 = CARTESIAN_POINT('',(0.,0.));
#1098 = VECTOR('',#1099,1.);
#1099 = DIRECTION('',(1.,0.));
#1100 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1101 = PCURVE('',#946,#1102);
#1102 = DEFINITIONAL_REPRESENTATION('',(#1103),#1107);
#1103 = LINE('',#1104,#1105);
#1104 = CARTESIAN_POINT('',(0.,0.));
#1105 = VECTOR('',#1106,1.);
#1106 = DIRECTION('',(1.,0.));
#1107 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1108 = ORIENTED_EDGE('',*,*,#1016,.T.);
#1109 = ORIENTED_EDGE('',*,*,#1110,.T.);
#1110 = EDGE_CURVE('',#1019,#959,#1111,.T.);
#1111 = SURFACE_CURVE('',#1112,(#1116,#1123),.PCURVE_S1.);
#1112 = LINE('',#1113,#1114);
#1113 = CARTESIAN_POINT('',(0.,0.,4.));
#1114 = VECTOR('',#1115,1.);
#1115 = DIRECTION('',(1.,0.,0.));
#1116 = PCURVE('',#974,#1117);
#1117 = DEFINITIONAL_REPRESENTATION('',(#1118),#1122);
#1118 = LINE('',#1119,#1120);
#1119 = CARTESIAN_POINT('',(0.,-4.));
#1120 = VECTOR('',#1121,1.);
#1121 = DIRECTION('',(1.,0.));
#1122 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1123 = PCURVE('',#1001,#1124);
#1124 = DEFINITIONAL_REPRESENTATION('',(#1125),#1129);
#1125 = LINE('',#1126,#1127);
#1126 = CARTESIAN_POINT('',(0.,0.));
#1127 = VECTOR('',#1128,1.);
#1128 = DIRECTION('',(1.,0.));
#1129 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1130 = ADVANCED_FACE('',(#1131),#946,.F.);
#1131 = FACE_BOUND('',#1132,.T.);
#1132 = EDGE_LOOP('',(#1133,#1134,#1135));
#1133 = ORIENTED_EDGE('',*,*,#929,.F.);
#1134 = ORIENTED_EDGE('',*,*,#1088,.F.);
#1135 = ORIENTED_EDGE('',*,*,#1063,.T.);
#1136 = ADVANCED_FACE('',(#1137),#1001,.T.);
#1137 = FACE_BOUND('',#1138,.F.);
#1138 = EDGE_LOOP('',(#1139,#1140,#1141));
#1139 = ORIENTED_EDGE('',*,*,#986,.F.);
#1140 = ORIENTED_EDGE('',*,*,#1110,.F.);
#1141 = ORIENTED_EDGE('',*,*,#1041,.T.);
#1142 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1146)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1143,#1144,#1145)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1143 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1144 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1145 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1146 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1143,
  'distance_accuracy_value','confusion accuracy');
#1147 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1148,#1150);
#1148 = ( REPRESENTATION_RELATIONSHIP('','',#887,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1149) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1149 = ITEM_DEFINED_TRANSFORMATION('','',#11,#43);
#1150 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1151);
#1151 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('8','圆柱体','',#5,#882,$);
#1152 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#884));
#1153 = SHAPE_DEFINITION_REPRESENTATION(#1154,#1160);
#1154 = PRODUCT_DEFINITION_SHAPE('','',#1155);
#1155 = PRODUCT_DEFINITION('design','',#1156,#1159);
#1156 = PRODUCT_DEFINITION_FORMATION('','',#1157);
#1157 = PRODUCT('圆柱体001','圆柱体001','',(#1158));
#1158 = PRODUCT_CONTEXT('',#2,'mechanical');
#1159 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1160 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1161),#1415);
#1161 = MANIFOLD_SOLID_BREP('',#1162);
#1162 = CLOSED_SHELL('',(#1163,#1285,#1356,#1403,#1409));
#1163 = ADVANCED_FACE('',(#1164),#1178,.T.);
#1164 = FACE_BOUND('',#1165,.F.);
#1165 = EDGE_LOOP('',(#1166,#1201,#1230,#1258));
#1166 = ORIENTED_EDGE('',*,*,#1167,.F.);
#1167 = EDGE_CURVE('',#1168,#1170,#1172,.T.);
#1168 = VERTEX_POINT('',#1169);
#1169 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#1170 = VERTEX_POINT('',#1171);
#1171 = CARTESIAN_POINT('',(-0.575,0.995929214352,4.));
#1172 = SURFACE_CURVE('',#1173,(#1177,#1189),.PCURVE_S1.);
#1173 = LINE('',#1174,#1175);
#1174 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#1175 = VECTOR('',#1176,1.);
#1176 = DIRECTION('',(0.,0.,1.));
#1177 = PCURVE('',#1178,#1183);
#1178 = CYLINDRICAL_SURFACE('',#1179,1.15);
#1179 = AXIS2_PLACEMENT_3D('',#1180,#1181,#1182);
#1180 = CARTESIAN_POINT('',(0.,0.,0.));
#1181 = DIRECTION('',(-0.,-0.,-1.));
#1182 = DIRECTION('',(1.,0.,-0.));
#1183 = DEFINITIONAL_REPRESENTATION('',(#1184),#1188);
#1184 = LINE('',#1185,#1186);
#1185 = CARTESIAN_POINT('',(-2.094395102393,0.));
#1186 = VECTOR('',#1187,1.);
#1187 = DIRECTION('',(-0.,-1.));
#1188 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1189 = PCURVE('',#1190,#1195);
#1190 = PLANE('',#1191);
#1191 = AXIS2_PLACEMENT_3D('',#1192,#1193,#1194);
#1192 = CARTESIAN_POINT('',(0.,0.,0.));
#1193 = DIRECTION('',(-0.866025403784,-0.5,0.));
#1194 = DIRECTION('',(-0.5,0.866025403784,0.));
#1195 = DEFINITIONAL_REPRESENTATION('',(#1196),#1200);
#1196 = LINE('',#1197,#1198);
#1197 = CARTESIAN_POINT('',(1.15,0.));
#1198 = VECTOR('',#1199,1.);
#1199 = DIRECTION('',(0.,-1.));
#1200 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1201 = ORIENTED_EDGE('',*,*,#1202,.F.);
#1202 = EDGE_CURVE('',#1203,#1168,#1205,.T.);
#1203 = VERTEX_POINT('',#1204);
#1204 = CARTESIAN_POINT('',(1.15,0.,0.));
#1205 = SURFACE_CURVE('',#1206,(#1211,#1218),.PCURVE_S1.);
#1206 = CIRCLE('',#1207,1.15);
#1207 = AXIS2_PLACEMENT_3D('',#1208,#1209,#1210);
#1208 = CARTESIAN_POINT('',(0.,0.,0.));
#1209 = DIRECTION('',(0.,0.,1.));
#1210 = DIRECTION('',(1.,0.,-0.));
#1211 = PCURVE('',#1178,#1212);
#1212 = DEFINITIONAL_REPRESENTATION('',(#1213),#1217);
#1213 = LINE('',#1214,#1215);
#1214 = CARTESIAN_POINT('',(-0.,0.));
#1215 = VECTOR('',#1216,1.);
#1216 = DIRECTION('',(-1.,0.));
#1217 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1218 = PCURVE('',#1219,#1224);
#1219 = PLANE('',#1220);
#1220 = AXIS2_PLACEMENT_3D('',#1221,#1222,#1223);
#1221 = CARTESIAN_POINT('',(0.,0.,0.));
#1222 = DIRECTION('',(0.,0.,1.));
#1223 = DIRECTION('',(1.,0.,-0.));
#1224 = DEFINITIONAL_REPRESENTATION('',(#1225),#1229);
#1225 = CIRCLE('',#1226,1.15);
#1226 = AXIS2_PLACEMENT_2D('',#1227,#1228);
#1227 = CARTESIAN_POINT('',(0.,0.));
#1228 = DIRECTION('',(1.,0.));
#1229 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1230 = ORIENTED_EDGE('',*,*,#1231,.T.);
#1231 = EDGE_CURVE('',#1203,#1232,#1234,.T.);
#1232 = VERTEX_POINT('',#1233);
#1233 = CARTESIAN_POINT('',(1.15,0.,4.));
#1234 = SURFACE_CURVE('',#1235,(#1239,#1246),.PCURVE_S1.);
#1235 = LINE('',#1236,#1237);
#1236 = CARTESIAN_POINT('',(1.15,0.,0.));
#1237 = VECTOR('',#1238,1.);
#1238 = DIRECTION('',(0.,0.,1.));
#1239 = PCURVE('',#1178,#1240);
#1240 = DEFINITIONAL_REPRESENTATION('',(#1241),#1245);
#1241 = LINE('',#1242,#1243);
#1242 = CARTESIAN_POINT('',(-0.,0.));
#1243 = VECTOR('',#1244,1.);
#1244 = DIRECTION('',(-0.,-1.));
#1245 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1246 = PCURVE('',#1247,#1252);
#1247 = PLANE('',#1248);
#1248 = AXIS2_PLACEMENT_3D('',#1249,#1250,#1251);
#1249 = CARTESIAN_POINT('',(0.,0.,0.));
#1250 = DIRECTION('',(-0.,1.,0.));
#1251 = DIRECTION('',(1.,0.,-0.));
#1252 = DEFINITIONAL_REPRESENTATION('',(#1253),#1257);
#1253 = LINE('',#1254,#1255);
#1254 = CARTESIAN_POINT('',(1.15,0.));
#1255 = VECTOR('',#1256,1.);
#1256 = DIRECTION('',(0.,-1.));
#1257 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1258 = ORIENTED_EDGE('',*,*,#1259,.T.);
#1259 = EDGE_CURVE('',#1232,#1170,#1260,.T.);
#1260 = SURFACE_CURVE('',#1261,(#1266,#1273),.PCURVE_S1.);
#1261 = CIRCLE('',#1262,1.15);
#1262 = AXIS2_PLACEMENT_3D('',#1263,#1264,#1265);
#1263 = CARTESIAN_POINT('',(0.,0.,4.));
#1264 = DIRECTION('',(0.,0.,1.));
#1265 = DIRECTION('',(1.,0.,0.));
#1266 = PCURVE('',#1178,#1267);
#1267 = DEFINITIONAL_REPRESENTATION('',(#1268),#1272);
#1268 = LINE('',#1269,#1270);
#1269 = CARTESIAN_POINT('',(-0.,-4.));
#1270 = VECTOR('',#1271,1.);
#1271 = DIRECTION('',(-1.,0.));
#1272 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1273 = PCURVE('',#1274,#1279);
#1274 = PLANE('',#1275);
#1275 = AXIS2_PLACEMENT_3D('',#1276,#1277,#1278);
#1276 = CARTESIAN_POINT('',(0.,0.,4.));
#1277 = DIRECTION('',(0.,0.,1.));
#1278 = DIRECTION('',(1.,0.,0.));
#1279 = DEFINITIONAL_REPRESENTATION('',(#1280),#1284);
#1280 = CIRCLE('',#1281,1.15);
#1281 = AXIS2_PLACEMENT_2D('',#1282,#1283);
#1282 = CARTESIAN_POINT('',(0.,0.));
#1283 = DIRECTION('',(1.,0.));
#1284 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1285 = ADVANCED_FACE('',(#1286),#1190,.T.);
#1286 = FACE_BOUND('',#1287,.T.);
#1287 = EDGE_LOOP('',(#1288,#1313,#1334,#1335));
#1288 = ORIENTED_EDGE('',*,*,#1289,.T.);
#1289 = EDGE_CURVE('',#1290,#1292,#1294,.T.);
#1290 = VERTEX_POINT('',#1291);
#1291 = CARTESIAN_POINT('',(0.,0.,0.));
#1292 = VERTEX_POINT('',#1293);
#1293 = CARTESIAN_POINT('',(0.,0.,4.));
#1294 = SURFACE_CURVE('',#1295,(#1299,#1306),.PCURVE_S1.);
#1295 = LINE('',#1296,#1297);
#1296 = CARTESIAN_POINT('',(0.,0.,0.));
#1297 = VECTOR('',#1298,1.);
#1298 = DIRECTION('',(0.,0.,1.));
#1299 = PCURVE('',#1190,#1300);
#1300 = DEFINITIONAL_REPRESENTATION('',(#1301),#1305);
#1301 = LINE('',#1302,#1303);
#1302 = CARTESIAN_POINT('',(0.,0.));
#1303 = VECTOR('',#1304,1.);
#1304 = DIRECTION('',(0.,-1.));
#1305 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1306 = PCURVE('',#1247,#1307);
#1307 = DEFINITIONAL_REPRESENTATION('',(#1308),#1312);
#1308 = LINE('',#1309,#1310);
#1309 = CARTESIAN_POINT('',(0.,0.));
#1310 = VECTOR('',#1311,1.);
#1311 = DIRECTION('',(0.,-1.));
#1312 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1313 = ORIENTED_EDGE('',*,*,#1314,.T.);
#1314 = EDGE_CURVE('',#1292,#1170,#1315,.T.);
#1315 = SURFACE_CURVE('',#1316,(#1320,#1327),.PCURVE_S1.);
#1316 = LINE('',#1317,#1318);
#1317 = CARTESIAN_POINT('',(0.,0.,4.));
#1318 = VECTOR('',#1319,1.);
#1319 = DIRECTION('',(-0.5,0.866025403784,0.));
#1320 = PCURVE('',#1190,#1321);
#1321 = DEFINITIONAL_REPRESENTATION('',(#1322),#1326);
#1322 = LINE('',#1323,#1324);
#1323 = CARTESIAN_POINT('',(0.,-4.));
#1324 = VECTOR('',#1325,1.);
#1325 = DIRECTION('',(1.,0.));
#1326 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1327 = PCURVE('',#1274,#1328);
#1328 = DEFINITIONAL_REPRESENTATION('',(#1329),#1333);
#1329 = LINE('',#1330,#1331);
#1330 = CARTESIAN_POINT('',(0.,0.));
#1331 = VECTOR('',#1332,1.);
#1332 = DIRECTION('',(-0.5,0.866025403784));
#1333 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1334 = ORIENTED_EDGE('',*,*,#1167,.F.);
#1335 = ORIENTED_EDGE('',*,*,#1336,.F.);
#1336 = EDGE_CURVE('',#1290,#1168,#1337,.T.);
#1337 = SURFACE_CURVE('',#1338,(#1342,#1349),.PCURVE_S1.);
#1338 = LINE('',#1339,#1340);
#1339 = CARTESIAN_POINT('',(0.,0.,0.));
#1340 = VECTOR('',#1341,1.);
#1341 = DIRECTION('',(-0.5,0.866025403784,0.));
#1342 = PCURVE('',#1190,#1343);
#1343 = DEFINITIONAL_REPRESENTATION('',(#1344),#1348);
#1344 = LINE('',#1345,#1346);
#1345 = CARTESIAN_POINT('',(0.,0.));
#1346 = VECTOR('',#1347,1.);
#1347 = DIRECTION('',(1.,0.));
#1348 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1349 = PCURVE('',#1219,#1350);
#1350 = DEFINITIONAL_REPRESENTATION('',(#1351),#1355);
#1351 = LINE('',#1352,#1353);
#1352 = CARTESIAN_POINT('',(0.,0.));
#1353 = VECTOR('',#1354,1.);
#1354 = DIRECTION('',(-0.5,0.866025403784));
#1355 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1356 = ADVANCED_FACE('',(#1357),#1247,.F.);
#1357 = FACE_BOUND('',#1358,.F.);
#1358 = EDGE_LOOP('',(#1359,#1360,#1381,#1382));
#1359 = ORIENTED_EDGE('',*,*,#1231,.F.);
#1360 = ORIENTED_EDGE('',*,*,#1361,.F.);
#1361 = EDGE_CURVE('',#1290,#1203,#1362,.T.);
#1362 = SURFACE_CURVE('',#1363,(#1367,#1374),.PCURVE_S1.);
#1363 = LINE('',#1364,#1365);
#1364 = CARTESIAN_POINT('',(0.,0.,0.));
#1365 = VECTOR('',#1366,1.);
#1366 = DIRECTION('',(1.,0.,-0.));
#1367 = PCURVE('',#1247,#1368);
#1368 = DEFINITIONAL_REPRESENTATION('',(#1369),#1373);
#1369 = LINE('',#1370,#1371);
#1370 = CARTESIAN_POINT('',(0.,0.));
#1371 = VECTOR('',#1372,1.);
#1372 = DIRECTION('',(1.,0.));
#1373 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1374 = PCURVE('',#1219,#1375);
#1375 = DEFINITIONAL_REPRESENTATION('',(#1376),#1380);
#1376 = LINE('',#1377,#1378);
#1377 = CARTESIAN_POINT('',(0.,0.));
#1378 = VECTOR('',#1379,1.);
#1379 = DIRECTION('',(1.,0.));
#1380 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1381 = ORIENTED_EDGE('',*,*,#1289,.T.);
#1382 = ORIENTED_EDGE('',*,*,#1383,.T.);
#1383 = EDGE_CURVE('',#1292,#1232,#1384,.T.);
#1384 = SURFACE_CURVE('',#1385,(#1389,#1396),.PCURVE_S1.);
#1385 = LINE('',#1386,#1387);
#1386 = CARTESIAN_POINT('',(0.,0.,4.));
#1387 = VECTOR('',#1388,1.);
#1388 = DIRECTION('',(1.,0.,0.));
#1389 = PCURVE('',#1247,#1390);
#1390 = DEFINITIONAL_REPRESENTATION('',(#1391),#1395);
#1391 = LINE('',#1392,#1393);
#1392 = CARTESIAN_POINT('',(0.,-4.));
#1393 = VECTOR('',#1394,1.);
#1394 = DIRECTION('',(1.,0.));
#1395 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1396 = PCURVE('',#1274,#1397);
#1397 = DEFINITIONAL_REPRESENTATION('',(#1398),#1402);
#1398 = LINE('',#1399,#1400);
#1399 = CARTESIAN_POINT('',(0.,0.));
#1400 = VECTOR('',#1401,1.);
#1401 = DIRECTION('',(1.,0.));
#1402 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1403 = ADVANCED_FACE('',(#1404),#1219,.F.);
#1404 = FACE_BOUND('',#1405,.T.);
#1405 = EDGE_LOOP('',(#1406,#1407,#1408));
#1406 = ORIENTED_EDGE('',*,*,#1202,.F.);
#1407 = ORIENTED_EDGE('',*,*,#1361,.F.);
#1408 = ORIENTED_EDGE('',*,*,#1336,.T.);
#1409 = ADVANCED_FACE('',(#1410),#1274,.T.);
#1410 = FACE_BOUND('',#1411,.F.);
#1411 = EDGE_LOOP('',(#1412,#1413,#1414));
#1412 = ORIENTED_EDGE('',*,*,#1259,.F.);
#1413 = ORIENTED_EDGE('',*,*,#1383,.F.);
#1414 = ORIENTED_EDGE('',*,*,#1314,.T.);
#1415 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1419)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1416,#1417,#1418)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1416 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1417 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1418 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1419 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1416,
  'distance_accuracy_value','confusion accuracy');
#1420 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1421,#1423);
#1421 = ( REPRESENTATION_RELATIONSHIP('','',#1160,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1422) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1422 = ITEM_DEFINED_TRANSFORMATION('','',#11,#47);
#1423 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1424);
#1424 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9','圆柱体001','',#5,#1155,$
  );
#1425 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1157));
#1426 = SHAPE_DEFINITION_REPRESENTATION(#1427,#1433);
#1427 = PRODUCT_DEFINITION_SHAPE('','',#1428);
#1428 = PRODUCT_DEFINITION('design','',#1429,#1432);
#1429 = PRODUCT_DEFINITION_FORMATION('','',#1430);
#1430 = PRODUCT('圆柱体002','圆柱体002','',(#1431));
#1431 = PRODUCT_CONTEXT('',#2,'mechanical');
#1432 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#1433 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#1434),#1688);
#1434 = MANIFOLD_SOLID_BREP('',#1435);
#1435 = CLOSED_SHELL('',(#1436,#1558,#1629,#1676,#1682));
#1436 = ADVANCED_FACE('',(#1437),#1451,.T.);
#1437 = FACE_BOUND('',#1438,.F.);
#1438 = EDGE_LOOP('',(#1439,#1474,#1503,#1531));
#1439 = ORIENTED_EDGE('',*,*,#1440,.F.);
#1440 = EDGE_CURVE('',#1441,#1443,#1445,.T.);
#1441 = VERTEX_POINT('',#1442);
#1442 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#1443 = VERTEX_POINT('',#1444);
#1444 = CARTESIAN_POINT('',(-0.575,0.995929214352,4.));
#1445 = SURFACE_CURVE('',#1446,(#1450,#1462),.PCURVE_S1.);
#1446 = LINE('',#1447,#1448);
#1447 = CARTESIAN_POINT('',(-0.575,0.995929214352,0.));
#1448 = VECTOR('',#1449,1.);
#1449 = DIRECTION('',(0.,0.,1.));
#1450 = PCURVE('',#1451,#1456);
#1451 = CYLINDRICAL_SURFACE('',#1452,1.15);
#1452 = AXIS2_PLACEMENT_3D('',#1453,#1454,#1455);
#1453 = CARTESIAN_POINT('',(0.,0.,0.));
#1454 = DIRECTION('',(-0.,-0.,-1.));
#1455 = DIRECTION('',(1.,0.,-0.));
#1456 = DEFINITIONAL_REPRESENTATION('',(#1457),#1461);
#1457 = LINE('',#1458,#1459);
#1458 = CARTESIAN_POINT('',(-2.094395102393,0.));
#1459 = VECTOR('',#1460,1.);
#1460 = DIRECTION('',(-0.,-1.));
#1461 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1462 = PCURVE('',#1463,#1468);
#1463 = PLANE('',#1464);
#1464 = AXIS2_PLACEMENT_3D('',#1465,#1466,#1467);
#1465 = CARTESIAN_POINT('',(0.,0.,0.));
#1466 = DIRECTION('',(-0.866025403784,-0.5,0.));
#1467 = DIRECTION('',(-0.5,0.866025403784,0.));
#1468 = DEFINITIONAL_REPRESENTATION('',(#1469),#1473);
#1469 = LINE('',#1470,#1471);
#1470 = CARTESIAN_POINT('',(1.15,0.));
#1471 = VECTOR('',#1472,1.);
#1472 = DIRECTION('',(0.,-1.));
#1473 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1474 = ORIENTED_EDGE('',*,*,#1475,.F.);
#1475 = EDGE_CURVE('',#1476,#1441,#1478,.T.);
#1476 = VERTEX_POINT('',#1477);
#1477 = CARTESIAN_POINT('',(1.15,0.,0.));
#1478 = SURFACE_CURVE('',#1479,(#1484,#1491),.PCURVE_S1.);
#1479 = CIRCLE('',#1480,1.15);
#1480 = AXIS2_PLACEMENT_3D('',#1481,#1482,#1483);
#1481 = CARTESIAN_POINT('',(0.,0.,0.));
#1482 = DIRECTION('',(0.,0.,1.));
#1483 = DIRECTION('',(1.,0.,-0.));
#1484 = PCURVE('',#1451,#1485);
#1485 = DEFINITIONAL_REPRESENTATION('',(#1486),#1490);
#1486 = LINE('',#1487,#1488);
#1487 = CARTESIAN_POINT('',(-0.,0.));
#1488 = VECTOR('',#1489,1.);
#1489 = DIRECTION('',(-1.,0.));
#1490 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1491 = PCURVE('',#1492,#1497);
#1492 = PLANE('',#1493);
#1493 = AXIS2_PLACEMENT_3D('',#1494,#1495,#1496);
#1494 = CARTESIAN_POINT('',(0.,0.,0.));
#1495 = DIRECTION('',(0.,0.,1.));
#1496 = DIRECTION('',(1.,0.,-0.));
#1497 = DEFINITIONAL_REPRESENTATION('',(#1498),#1502);
#1498 = CIRCLE('',#1499,1.15);
#1499 = AXIS2_PLACEMENT_2D('',#1500,#1501);
#1500 = CARTESIAN_POINT('',(0.,0.));
#1501 = DIRECTION('',(1.,0.));
#1502 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1503 = ORIENTED_EDGE('',*,*,#1504,.T.);
#1504 = EDGE_CURVE('',#1476,#1505,#1507,.T.);
#1505 = VERTEX_POINT('',#1506);
#1506 = CARTESIAN_POINT('',(1.15,0.,4.));
#1507 = SURFACE_CURVE('',#1508,(#1512,#1519),.PCURVE_S1.);
#1508 = LINE('',#1509,#1510);
#1509 = CARTESIAN_POINT('',(1.15,0.,0.));
#1510 = VECTOR('',#1511,1.);
#1511 = DIRECTION('',(0.,0.,1.));
#1512 = PCURVE('',#1451,#1513);
#1513 = DEFINITIONAL_REPRESENTATION('',(#1514),#1518);
#1514 = LINE('',#1515,#1516);
#1515 = CARTESIAN_POINT('',(-0.,0.));
#1516 = VECTOR('',#1517,1.);
#1517 = DIRECTION('',(-0.,-1.));
#1518 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1519 = PCURVE('',#1520,#1525);
#1520 = PLANE('',#1521);
#1521 = AXIS2_PLACEMENT_3D('',#1522,#1523,#1524);
#1522 = CARTESIAN_POINT('',(0.,0.,0.));
#1523 = DIRECTION('',(-0.,1.,0.));
#1524 = DIRECTION('',(1.,0.,-0.));
#1525 = DEFINITIONAL_REPRESENTATION('',(#1526),#1530);
#1526 = LINE('',#1527,#1528);
#1527 = CARTESIAN_POINT('',(1.15,0.));
#1528 = VECTOR('',#1529,1.);
#1529 = DIRECTION('',(0.,-1.));
#1530 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1531 = ORIENTED_EDGE('',*,*,#1532,.T.);
#1532 = EDGE_CURVE('',#1505,#1443,#1533,.T.);
#1533 = SURFACE_CURVE('',#1534,(#1539,#1546),.PCURVE_S1.);
#1534 = CIRCLE('',#1535,1.15);
#1535 = AXIS2_PLACEMENT_3D('',#1536,#1537,#1538);
#1536 = CARTESIAN_POINT('',(0.,0.,4.));
#1537 = DIRECTION('',(0.,0.,1.));
#1538 = DIRECTION('',(1.,0.,0.));
#1539 = PCURVE('',#1451,#1540);
#1540 = DEFINITIONAL_REPRESENTATION('',(#1541),#1545);
#1541 = LINE('',#1542,#1543);
#1542 = CARTESIAN_POINT('',(-0.,-4.));
#1543 = VECTOR('',#1544,1.);
#1544 = DIRECTION('',(-1.,0.));
#1545 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1546 = PCURVE('',#1547,#1552);
#1547 = PLANE('',#1548);
#1548 = AXIS2_PLACEMENT_3D('',#1549,#1550,#1551);
#1549 = CARTESIAN_POINT('',(0.,0.,4.));
#1550 = DIRECTION('',(0.,0.,1.));
#1551 = DIRECTION('',(1.,0.,0.));
#1552 = DEFINITIONAL_REPRESENTATION('',(#1553),#1557);
#1553 = CIRCLE('',#1554,1.15);
#1554 = AXIS2_PLACEMENT_2D('',#1555,#1556);
#1555 = CARTESIAN_POINT('',(0.,0.));
#1556 = DIRECTION('',(1.,0.));
#1557 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1558 = ADVANCED_FACE('',(#1559),#1463,.T.);
#1559 = FACE_BOUND('',#1560,.T.);
#1560 = EDGE_LOOP('',(#1561,#1586,#1607,#1608));
#1561 = ORIENTED_EDGE('',*,*,#1562,.T.);
#1562 = EDGE_CURVE('',#1563,#1565,#1567,.T.);
#1563 = VERTEX_POINT('',#1564);
#1564 = CARTESIAN_POINT('',(0.,0.,0.));
#1565 = VERTEX_POINT('',#1566);
#1566 = CARTESIAN_POINT('',(0.,0.,4.));
#1567 = SURFACE_CURVE('',#1568,(#1572,#1579),.PCURVE_S1.);
#1568 = LINE('',#1569,#1570);
#1569 = CARTESIAN_POINT('',(0.,0.,0.));
#1570 = VECTOR('',#1571,1.);
#1571 = DIRECTION('',(0.,0.,1.));
#1572 = PCURVE('',#1463,#1573);
#1573 = DEFINITIONAL_REPRESENTATION('',(#1574),#1578);
#1574 = LINE('',#1575,#1576);
#1575 = CARTESIAN_POINT('',(0.,0.));
#1576 = VECTOR('',#1577,1.);
#1577 = DIRECTION('',(0.,-1.));
#1578 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1579 = PCURVE('',#1520,#1580);
#1580 = DEFINITIONAL_REPRESENTATION('',(#1581),#1585);
#1581 = LINE('',#1582,#1583);
#1582 = CARTESIAN_POINT('',(0.,0.));
#1583 = VECTOR('',#1584,1.);
#1584 = DIRECTION('',(0.,-1.));
#1585 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1586 = ORIENTED_EDGE('',*,*,#1587,.T.);
#1587 = EDGE_CURVE('',#1565,#1443,#1588,.T.);
#1588 = SURFACE_CURVE('',#1589,(#1593,#1600),.PCURVE_S1.);
#1589 = LINE('',#1590,#1591);
#1590 = CARTESIAN_POINT('',(0.,0.,4.));
#1591 = VECTOR('',#1592,1.);
#1592 = DIRECTION('',(-0.5,0.866025403784,0.));
#1593 = PCURVE('',#1463,#1594);
#1594 = DEFINITIONAL_REPRESENTATION('',(#1595),#1599);
#1595 = LINE('',#1596,#1597);
#1596 = CARTESIAN_POINT('',(0.,-4.));
#1597 = VECTOR('',#1598,1.);
#1598 = DIRECTION('',(1.,0.));
#1599 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1600 = PCURVE('',#1547,#1601);
#1601 = DEFINITIONAL_REPRESENTATION('',(#1602),#1606);
#1602 = LINE('',#1603,#1604);
#1603 = CARTESIAN_POINT('',(0.,0.));
#1604 = VECTOR('',#1605,1.);
#1605 = DIRECTION('',(-0.5,0.866025403784));
#1606 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1607 = ORIENTED_EDGE('',*,*,#1440,.F.);
#1608 = ORIENTED_EDGE('',*,*,#1609,.F.);
#1609 = EDGE_CURVE('',#1563,#1441,#1610,.T.);
#1610 = SURFACE_CURVE('',#1611,(#1615,#1622),.PCURVE_S1.);
#1611 = LINE('',#1612,#1613);
#1612 = CARTESIAN_POINT('',(0.,0.,0.));
#1613 = VECTOR('',#1614,1.);
#1614 = DIRECTION('',(-0.5,0.866025403784,0.));
#1615 = PCURVE('',#1463,#1616);
#1616 = DEFINITIONAL_REPRESENTATION('',(#1617),#1621);
#1617 = LINE('',#1618,#1619);
#1618 = CARTESIAN_POINT('',(0.,0.));
#1619 = VECTOR('',#1620,1.);
#1620 = DIRECTION('',(1.,0.));
#1621 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1622 = PCURVE('',#1492,#1623);
#1623 = DEFINITIONAL_REPRESENTATION('',(#1624),#1628);
#1624 = LINE('',#1625,#1626);
#1625 = CARTESIAN_POINT('',(0.,0.));
#1626 = VECTOR('',#1627,1.);
#1627 = DIRECTION('',(-0.5,0.866025403784));
#1628 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1629 = ADVANCED_FACE('',(#1630),#1520,.F.);
#1630 = FACE_BOUND('',#1631,.F.);
#1631 = EDGE_LOOP('',(#1632,#1633,#1654,#1655));
#1632 = ORIENTED_EDGE('',*,*,#1504,.F.);
#1633 = ORIENTED_EDGE('',*,*,#1634,.F.);
#1634 = EDGE_CURVE('',#1563,#1476,#1635,.T.);
#1635 = SURFACE_CURVE('',#1636,(#1640,#1647),.PCURVE_S1.);
#1636 = LINE('',#1637,#1638);
#1637 = CARTESIAN_POINT('',(0.,0.,0.));
#1638 = VECTOR('',#1639,1.);
#1639 = DIRECTION('',(1.,0.,-0.));
#1640 = PCURVE('',#1520,#1641);
#1641 = DEFINITIONAL_REPRESENTATION('',(#1642),#1646);
#1642 = LINE('',#1643,#1644);
#1643 = CARTESIAN_POINT('',(0.,0.));
#1644 = VECTOR('',#1645,1.);
#1645 = DIRECTION('',(1.,0.));
#1646 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1647 = PCURVE('',#1492,#1648);
#1648 = DEFINITIONAL_REPRESENTATION('',(#1649),#1653);
#1649 = LINE('',#1650,#1651);
#1650 = CARTESIAN_POINT('',(0.,0.));
#1651 = VECTOR('',#1652,1.);
#1652 = DIRECTION('',(1.,0.));
#1653 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1654 = ORIENTED_EDGE('',*,*,#1562,.T.);
#1655 = ORIENTED_EDGE('',*,*,#1656,.T.);
#1656 = EDGE_CURVE('',#1565,#1505,#1657,.T.);
#1657 = SURFACE_CURVE('',#1658,(#1662,#1669),.PCURVE_S1.);
#1658 = LINE('',#1659,#1660);
#1659 = CARTESIAN_POINT('',(0.,0.,4.));
#1660 = VECTOR('',#1661,1.);
#1661 = DIRECTION('',(1.,0.,0.));
#1662 = PCURVE('',#1520,#1663);
#1663 = DEFINITIONAL_REPRESENTATION('',(#1664),#1668);
#1664 = LINE('',#1665,#1666);
#1665 = CARTESIAN_POINT('',(0.,-4.));
#1666 = VECTOR('',#1667,1.);
#1667 = DIRECTION('',(1.,0.));
#1668 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1669 = PCURVE('',#1547,#1670);
#1670 = DEFINITIONAL_REPRESENTATION('',(#1671),#1675);
#1671 = LINE('',#1672,#1673);
#1672 = CARTESIAN_POINT('',(0.,0.));
#1673 = VECTOR('',#1674,1.);
#1674 = DIRECTION('',(1.,0.));
#1675 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#1676 = ADVANCED_FACE('',(#1677),#1492,.F.);
#1677 = FACE_BOUND('',#1678,.T.);
#1678 = EDGE_LOOP('',(#1679,#1680,#1681));
#1679 = ORIENTED_EDGE('',*,*,#1475,.F.);
#1680 = ORIENTED_EDGE('',*,*,#1634,.F.);
#1681 = ORIENTED_EDGE('',*,*,#1609,.T.);
#1682 = ADVANCED_FACE('',(#1683),#1547,.T.);
#1683 = FACE_BOUND('',#1684,.F.);
#1684 = EDGE_LOOP('',(#1685,#1686,#1687));
#1685 = ORIENTED_EDGE('',*,*,#1532,.F.);
#1686 = ORIENTED_EDGE('',*,*,#1656,.F.);
#1687 = ORIENTED_EDGE('',*,*,#1587,.T.);
#1688 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#1692)) 
GLOBAL_UNIT_ASSIGNED_CONTEXT((#1689,#1690,#1691)) REPRESENTATION_CONTEXT
('Context #1','3D Context with UNIT and UNCERTAINTY') );
#1689 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#1690 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#1691 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#1692 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#1689,
  'distance_accuracy_value','confusion accuracy');
#1693 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#1694,#1696);
#1694 = ( REPRESENTATION_RELATIONSHIP('','',#1433,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#1695) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#1695 = ITEM_DEFINED_TRANSFORMATION('','',#11,#51);
#1696 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',
  #1697);
#1697 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('10','圆柱体002','',#5,#1428,$
  );
#1698 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#1430));
#1699 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1700),#401);
#1700 = STYLED_ITEM('color',(#1701),#303);
#1701 = PRESENTATION_STYLE_ASSIGNMENT((#1702,#1708));
#1702 = SURFACE_STYLE_USAGE(.BOTH.,#1703);
#1703 = SURFACE_SIDE_STYLE('',(#1704));
#1704 = SURFACE_STYLE_FILL_AREA(#1705);
#1705 = FILL_AREA_STYLE('',(#1706));
#1706 = FILL_AREA_STYLE_COLOUR('',#1707);
#1707 = COLOUR_RGB('',0.,0.666666687201,1.);
#1708 = CURVE_STYLE('',#1709,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1709 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1710 = COLOUR_RGB('',9.803921802644E-02,9.803921802644E-02,
  9.803921802644E-02);
#1711 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1712),#284);
#1712 = STYLED_ITEM('color',(#1713),#186);
#1713 = PRESENTATION_STYLE_ASSIGNMENT((#1714,#1720));
#1714 = SURFACE_STYLE_USAGE(.BOTH.,#1715);
#1715 = SURFACE_SIDE_STYLE('',(#1716));
#1716 = SURFACE_STYLE_FILL_AREA(#1717);
#1717 = FILL_AREA_STYLE('',(#1718));
#1718 = FILL_AREA_STYLE_COLOUR('',#1719);
#1719 = DRAUGHTING_PRE_DEFINED_COLOUR('yellow');
#1720 = CURVE_STYLE('',#1721,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1721 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1722 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1723),#518);
#1723 = STYLED_ITEM('color',(#1724),#420);
#1724 = PRESENTATION_STYLE_ASSIGNMENT((#1725,#1730));
#1725 = SURFACE_STYLE_USAGE(.BOTH.,#1726);
#1726 = SURFACE_SIDE_STYLE('',(#1727));
#1727 = SURFACE_STYLE_FILL_AREA(#1728);
#1728 = FILL_AREA_STYLE('',(#1729));
#1729 = FILL_AREA_STYLE_COLOUR('',#1719);
#1730 = CURVE_STYLE('',#1731,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1731 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1732 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1733),#635);
#1733 = STYLED_ITEM('color',(#1734),#537);
#1734 = PRESENTATION_STYLE_ASSIGNMENT((#1735,#1740));
#1735 = SURFACE_STYLE_USAGE(.BOTH.,#1736);
#1736 = SURFACE_SIDE_STYLE('',(#1737));
#1737 = SURFACE_STYLE_FILL_AREA(#1738);
#1738 = FILL_AREA_STYLE('',(#1739));
#1739 = FILL_AREA_STYLE_COLOUR('',#1707);
#1740 = CURVE_STYLE('',#1741,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1741 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1742 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1743),#1688);
#1743 = STYLED_ITEM('color',(#1744),#1434);
#1744 = PRESENTATION_STYLE_ASSIGNMENT((#1745,#1750));
#1745 = SURFACE_STYLE_USAGE(.BOTH.,#1746);
#1746 = SURFACE_SIDE_STYLE('',(#1747));
#1747 = SURFACE_STYLE_FILL_AREA(#1748);
#1748 = FILL_AREA_STYLE('',(#1749));
#1749 = FILL_AREA_STYLE_COLOUR('',#1719);
#1750 = CURVE_STYLE('',#1751,POSITIVE_LENGTH_MEASURE(0.1),#1752);
#1751 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1752 = COLOUR_RGB('',0.333333345507,1.,0.);
#1753 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1754),#1142);
#1754 = STYLED_ITEM('color',(#1755),#888);
#1755 = PRESENTATION_STYLE_ASSIGNMENT((#1756,#1762));
#1756 = SURFACE_STYLE_USAGE(.BOTH.,#1757);
#1757 = SURFACE_SIDE_STYLE('',(#1758));
#1758 = SURFACE_STYLE_FILL_AREA(#1759);
#1759 = FILL_AREA_STYLE('',(#1760));
#1760 = FILL_AREA_STYLE_COLOUR('',#1761);
#1761 = DRAUGHTING_PRE_DEFINED_COLOUR('red');
#1762 = CURVE_STYLE('',#1763,POSITIVE_LENGTH_MEASURE(0.1),#1764);
#1763 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1764 = COLOUR_RGB('',0.509803943113,1.,0.54901964059);
#1765 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1766),#167);
#1766 = STYLED_ITEM('color',(#1767),#69);
#1767 = PRESENTATION_STYLE_ASSIGNMENT((#1768,#1773));
#1768 = SURFACE_STYLE_USAGE(.BOTH.,#1769);
#1769 = SURFACE_SIDE_STYLE('',(#1770));
#1770 = SURFACE_STYLE_FILL_AREA(#1771);
#1771 = FILL_AREA_STYLE('',(#1772));
#1772 = FILL_AREA_STYLE_COLOUR('',#1707);
#1773 = CURVE_STYLE('',#1774,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1774 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1775 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1776),#1415);
#1776 = STYLED_ITEM('color',(#1777),#1161);
#1777 = PRESENTATION_STYLE_ASSIGNMENT((#1778,#1784));
#1778 = SURFACE_STYLE_USAGE(.BOTH.,#1779);
#1779 = SURFACE_SIDE_STYLE('',(#1780));
#1780 = SURFACE_STYLE_FILL_AREA(#1781);
#1781 = FILL_AREA_STYLE('',(#1782));
#1782 = FILL_AREA_STYLE_COLOUR('',#1783);
#1783 = COLOUR_RGB('',8.235294228292E-02,0.129411774038,0.800000010877);
#1784 = CURVE_STYLE('',#1785,POSITIVE_LENGTH_MEASURE(0.1),#1752);
#1785 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1786 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1787),#752);
#1787 = STYLED_ITEM('color',(#1788),#654);
#1788 = PRESENTATION_STYLE_ASSIGNMENT((#1789,#1794));
#1789 = SURFACE_STYLE_USAGE(.BOTH.,#1790);
#1790 = SURFACE_SIDE_STYLE('',(#1791));
#1791 = SURFACE_STYLE_FILL_AREA(#1792);
#1792 = FILL_AREA_STYLE('',(#1793));
#1793 = FILL_AREA_STYLE_COLOUR('',#1719);
#1794 = CURVE_STYLE('',#1795,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1795 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#1796 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(
    #1797),#869);
#1797 = STYLED_ITEM('color',(#1798),#771);
#1798 = PRESENTATION_STYLE_ASSIGNMENT((#1799,#1804));
#1799 = SURFACE_STYLE_USAGE(.BOTH.,#1800);
#1800 = SURFACE_SIDE_STYLE('',(#1801));
#1801 = SURFACE_STYLE_FILL_AREA(#1802);
#1802 = FILL_AREA_STYLE('',(#1803));
#1803 = FILL_AREA_STYLE_COLOUR('',#1707);
#1804 = CURVE_STYLE('',#1805,POSITIVE_LENGTH_MEASURE(0.1),#1710);
#1805 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
