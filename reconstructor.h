#ifndef RECONSTRUCTOR_H
#define RECONSTRUCTOR_H

#include <mutex>
#include <condition_variable>
#ifndef EIGEN_USE_BLAS
	#define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>

#include "point.h"
#include "respiration.h"
#include "catheter.h"
#include "volume.h"

class FAMReconstructor
{
public:
	//typedef std::shared_ptr<FAMReconstructor> Ptr;
	using Ptr = std::shared_ptr<FAMReconstructor> ;
	FAMReconstructor(Catheter::Ptr catheter, const std::vector<float>& resolutions, int respSamplingRate, const Eigen::AlignedBox3f& bounds);
	~FAMReconstructor();

	Catheter::Ptr catheter() const
	{
		return mCatheter;
	}

	const std::vector<float>& availableResolutions() const
	{
		return mAvailableResolutions;
	}

	int currentResolutionId()
	{
		std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
		return mCurrentResolutionId;
	}

	float currentResolution()
	{
		std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
		return mAvailableResolutions[mCurrentResolutionId];
	}

	FAMVolume::Ptr currentVolume()
	{
		std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
		return mCurrentVolume;
	}

	void changeResolution(int newResolutionId, bool join);
	
	const Respiration& respiration() const
	{
		return this->mRespiration;
	}

	void onReceiveScanPoint(const FAMPoint& fpt);
	void onReceiveRespiration(float resp, double timestamp);
	
	vtkSmartPointer<vtkPolyData> localMesh();

	vtkSmartPointer<vtkPolyData> globalMesh();

	vtkSmartPointer<vtkPolyData> transitionMesh();

	void completeReconstruction();

	float respThreshold() const
	{
		return this->mRespThreshold;
	}

	void setRespThreshold(float value);

	FAMVolume::Ptr baseVolume()
	{
		return this->mBaseVolume;
	}

	bool isFinished()
	{
		std::lock_guard<std::recursive_mutex> lock(this->mFinishMutex);
		return this->mIsFinished;
	}
private:
	bool checkRespirationThreshold(double timestamp);
	void updateCurrentVolume();
	void updateLocalMesh();
	void updateGlobalMesh(bool newThread=true);
	void changeResolutionImp(int newResolutionId, bool join);

	std::recursive_mutex mFinishMutex;
	bool mIsFinished;
	std::recursive_mutex mResolutionMutex;
	std::shared_ptr<std::thread> mChangeResolutionWorker;
	std::recursive_mutex mChangeResolutionWorkerMutex;
	std::vector<float> mAvailableResolutions;
	int mCurrentResolutionId;
	Catheter::Ptr mCatheter;
	Eigen::AlignedBox3f mBounds;
	int mLastMergedScanPointIndex;
	
	std::vector<FAMPoint> mRawScanData;
	std::recursive_timed_mutex mScanMutex;
	Respiration mRespiration;
	std::vector<std::tuple<float, double>> mRespirationCache;
	std::recursive_timed_mutex mRespMutex;
	FAMVolume::Ptr mCurrentVolume;
	FAMVolume::Ptr mBaseVolume;

	std::mutex mLocalWorkerMutex;
	std::condition_variable mLocalCondition;
	std::shared_ptr<std::thread> mLocalWorker;
	std::recursive_timed_mutex mGlobalWorkerMutex;
	std::condition_variable_any mGlobalCondition;
	std::shared_ptr<std::thread> mGlobalWorker;

	std::recursive_mutex mLocalMutex;
	vtkSmartPointer<vtkPolyData> mLocalMesh;
	std::recursive_mutex mTransitionMutex;
	vtkSmartPointer<vtkPolyData> mTransitionMesh;
	std::recursive_mutex mGlobalMutex;
	vtkSmartPointer<vtkPolyData> mGlobalMesh;

	float mRespThreshold;
};
#endif