#ifndef FAMVOLUME_H
#define FAMVOLUME_H

#ifndef EIGEN_USE_BLAS
#define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>

#include <vector>

#include <nanoflann.hpp>
#include <vtkPolyData.h>
#include <vtkImageData.h>

#include "point.h"
#include "catheter.h"
#include "point_cloud_manager.h"

class FAMVolume
{
public:
    //typedef std::shared_ptr<FAMVolume> Ptr;
    using Ptr=std::shared_ptr<FAMVolume> ;
    FAMVolume(float spacing, Catheter::Ptr catheter, const Eigen::AlignedBox3f& bounds);
    ~FAMVolume();

    Catheter::Ptr catheter() const
    {
        return this->mCatheter;
    }

    float spacing() const;

    Eigen::Vector3f origin() const;

    Eigen::Vector3f center() const;

    void setCenter(const Eigen::Vector3f& newCenter);
    
    void mergeCatheterVolume(const Eigen::Isometry3f& transform, int scanPointIndex);

    void clear();

    vtkSmartPointer<vtkImageData> image()
    {
        return this->mImage;
    }

    vtkSmartPointer<vtkPolyData> reconstruct();

    void addScanPoint2LocalVolume(const FAMPoint& fpt, int scanPointIndex);

    void mergeMesh(vtkSmartPointer<vtkPolyData> mesh);
    
    // 获取导管体素化后的世界坐标点，用于可视化
    vtkSmartPointer<vtkPoints> catheterWorldPoints() const;
    
    // 获取所有累积的导管世界坐标点，用于完整可视化
    vtkSmartPointer<vtkPoints> allCatheterWorldPoints() const;

private:
    vtkSmartPointer<vtkPolyData> reconstruct(vtkSmartPointer<vtkImageData> image, std::recursive_mutex& mutex);
    std::chrono::steady_clock::time_point mStartTimeStamp;
    Eigen::AlignedBox3f mBounds;
    Catheter::Ptr mCatheter;
    Eigen::Affine3f mIndex2World;
    Eigen::Affine3f mWorld2Index;
    vtkSmartPointer<vtkPoints> mCatheterVolume;
    vtkSmartPointer<vtkImageData> mImage;
    std::recursive_mutex mImageMutex;
    vtkSmartPointer<vtkImageData> mWhiteImage;
   
    FAMPoint mLastPt;

    unsigned int mModCount;
    
    // 存储导管体素化后的世界坐标点，用于可视化
    vtkSmartPointer<vtkPoints> mCatheterWorldPoints;
    mutable std::recursive_mutex mCatheterPointsMutex;

    // 存储所有累积的导管点，用于完整可视化
    vtkSmartPointer<vtkPoints> mAllCatheterWorldPoints;
    mutable std::recursive_mutex mAllCatheterPointsMutex;

    // 使用专用的点云管理器替代原有的缓存机制
    std::unique_ptr<PointCloudManager> mPointCloudManager;

    // 点云数据管理
    void updatePointsCache();
    bool getPointsCacheData(std::vector<Eigen::Vector3f>& currentPoints,
                           std::vector<Eigen::Vector3f>& allPoints);
};

#endif // !FAMVOLUME_H
