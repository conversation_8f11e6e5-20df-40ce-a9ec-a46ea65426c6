#ifndef SCENEWIDGET_H
#define SCENEWIDGET_H
#include <string>
#include <unordered_map>
#include <QVTKOpenGLNativeWidget.h>
#include <vtkDataSet.h>
#include <vtkRenderer.h>
#include <vtkSmartPointer.h>
#include <vtkActor.h>
#include <vtkVolume.h>
#include <vtkPolyData.h>
#include <vtkImageData.h>

#ifndef EIGEN_USE_BLAS
    #define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>

class SceneWidget : public QVTKOpenGLNativeWidget {
    Q_OBJECT
public:
    explicit SceneWidget(QWidget* parent = nullptr);

    //! Add a data set to the scene
    /*!
    \param[in] dataSet The data set to add
  */
    vtkSmartPointer<vtkActor> addDataSet(vtkPolyData* dataSet, const std::string& name, const Eigen::Vector4d& rgba);
    vtkSmartPointer<vtkActor> addDataSet(vtkPolyData* dataSet, const std::string& name, const std::string& scalarName, const std::string& colormap);
    vtkSmartPointer<vtkVolume> addVolume(vtkImageData* dataSet, const std::string& name);
    bool updateDataSet(vtkPolyData* dataSet, const std::string& name, double opacity=-1.0);

    //! Remove the data set from the scene
    void removeDataSet(const std::string& name);

    void backgroundColor(double& r, double& g, double& b);
    void setBackgroundColor(double r, double g, double b);

    void update();
    void clear();
public slots:
    //! Zoom to the extent of the data set in the scene
    void zoomBox(const Eigen::AlignedBox3f& box);
    void zoomFit();

private:
    vtkSmartPointer<vtkRenderer> mRenderer;
    std::unordered_map<std::string, vtkSmartPointer<vtkActor>> mActors;
    std::unordered_map<std::string, vtkSmartPointer<vtkVolume>> mVolumes;
};

#endif // SCENEWIDGET_H
