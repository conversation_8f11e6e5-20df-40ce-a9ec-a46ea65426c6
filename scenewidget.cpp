#include "scenewidget.h"

#include <vtkCamera.h>
#include <vtkDataSetMapper.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkImageData.h>
#include <vtkGPUVolumeRayCastMapper.h>
#include <vtkFixedPointVolumeRayCastMapper.h>
#include <vtkGenericOpenGLRenderWindow.h>
#include <vtkProperty.h>
#include <vtkRenderWindowInteractor.h>

#include <vtkSmartVolumeMapper.h>


SceneWidget::SceneWidget(QWidget* parent)
    : QVTKOpenGLNativeWidget(parent)
{
    vtkNew<vtkGenericOpenGLRenderWindow> window;
    setRenderWindow(window.Get());

    // Camera
    vtkSmartPointer<vtkCamera> camera = vtkSmartPointer<vtkCamera>::New();
    camera->SetViewUp(0, 1, 0);
    camera->SetPosition(0, 0, 10);
    camera->SetFocalPoint(0, 0, 0);

    // Renderer
    mRenderer = vtkSmartPointer<vtkRenderer>::New();
    mRenderer->SetActiveCamera(camera);
    mRenderer->GetActiveCamera()->Zoom(0.8);// 10:36 04/06 2024
    renderWindow()->AddRenderer(mRenderer);
}

vtkSmartPointer<vtkActor> SceneWidget::addDataSet(vtkPolyData* dataSet, const std::string& name, const Eigen::Vector4d& rgba)
{
    // Actor
    vtkSmartPointer<vtkActor> actor = vtkSmartPointer<vtkActor>::New();
    // Mapper
    vtkPolyDataMapper* mapper = vtkPolyDataMapper::New();
    mapper->SetInputDataObject(dataSet);
    if (name != "catheter")
    {
    mapper->ScalarVisibilityOff();   
    }
    actor->SetMapper(mapper);
    if (name != "catheter")
    {
        actor->GetProperty()->SetColor(rgba[0], rgba[1], rgba[2]);
    }
    actor->GetProperty()->SetColor(rgba[0], rgba[1], rgba[2]);
    actor->GetProperty()->SetOpacity(rgba[3]);
    mRenderer->AddActor(actor);
    this->mActors[name] = actor;
    
    //mRenderer->ResetCamera(dataSet->GetBounds());

    renderWindow()->Render();
    
    return actor;
}

vtkSmartPointer<vtkActor> SceneWidget::addDataSet(vtkPolyData* dataSet, const std::string& name, const std::string& scalarName, const std::string& colormap)
{
    // Actor
    vtkSmartPointer<vtkActor> actor = vtkSmartPointer<vtkActor>::New();
    // Mapper
    vtkPolyDataMapper* mapper = vtkPolyDataMapper::New();
    mapper->SetInputDataObject(dataSet);
    mapper->ScalarVisibilityOn();
    mapper->SetScalarModeToUsePointFieldData();
    //mapper->ColorByArrayComponent(scalarName.c_str(), 0);
    //mapper->SetColorModeToDirectScalars();
    //mapper->SetColorModeToMapScalars();
    mapper->SelectColorArray(scalarName.c_str());
    double scalarRange[2];
    dataSet->GetScalarRange(scalarRange);
    mapper->SetScalarRange(scalarRange);
    actor->SetMapper(mapper);
    actor->GetProperty()->SetAmbient(0.4);
    actor->GetProperty()->SetDiffuse(0.6);
    actor->GetProperty()->SetSpecular(0.3*2);//0.3
    actor->GetProperty()->SetSpecularPower(2*20);//20

    
    mRenderer->AddActor(actor);
    this->mActors[name] = actor;

    mRenderer->ResetCamera(dataSet->GetBounds());

    renderWindow()->Render();

    return actor;
}

vtkSmartPointer<vtkVolume> SceneWidget::addVolume(vtkImageData* dataSet, const std::string& name)
{
  //  vtkVolumeMapper* mapper = vtkGPUVolumeRayCastMapper::New();
  //  vtkSmartPointer<vtkGPUVolumeRayCastMapper> mapper = vtkSmartPointer<vtkSmartVolumeMapper>::New();
    vtkSmartPointer<vtkFixedPointVolumeRayCastMapper> mapper = vtkSmartPointer<vtkFixedPointVolumeRayCastMapper>::New();
    mapper->SetInputData(dataSet);
   // mapper->SetInputDataObject(dataSet);
    vtkSmartPointer<vtkVolume> volume = vtkSmartPointer<vtkVolume>::New();
    volume->SetMapper(mapper);
    mRenderer->AddVolume(volume);
    this->mVolumes[name] = volume;
    mRenderer->ResetCamera(dataSet->GetBounds());
    renderWindow()->Render();
    return volume;
}

bool SceneWidget::updateDataSet(vtkPolyData* dataSet, const std::string& name, double opacity)
{
    auto it = this->mActors.find(name);
    if (it != this->mActors.cend())
    {
        vtkSmartPointer<vtkActor> actor = it->second;
        if (opacity >= 0.0)
            actor->GetProperty()->SetOpacity(opacity);

        if (actor->GetMapper()->GetInput() != dataSet)
        {
            actor->GetMapper()->SetInputDataObject(dataSet);            
            actor->GetMapper()->Update();            
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }

    
}

void SceneWidget::removeDataSet(const std::string& name)
{
    auto it = this->mActors.find(name);
    if (it != this->mActors.cend())
    {
        mRenderer->RemoveActor(it->second);
    }
    else
    {
        auto it = this->mVolumes.find(name);
        if (it != this->mVolumes.cend())
        {
            mRenderer->RemoveVolume(it->second);
        }
    }

    renderWindow()->Render();
}

void SceneWidget::backgroundColor(double& r, double& g, double& b)
{
    mRenderer->GetBackground(r, g, b);
}

void SceneWidget::setBackgroundColor(double r, double g, double b)
{
    mRenderer->SetBackground(r, g, b);
}

void SceneWidget::update()
{
    //QVTKOpenGLNativeWidget::update();
    //for (auto actor = this->mActors.begin(); actor != this->mActors.end(); actor++)
    //{
    //    actor->second->Render(this->mRenderer, actor->second->GetMapper());
    //}
    this->renderWindow()->Render();
}

void SceneWidget::clear()
{
    if (!this->mActors.empty())
    {
        for (auto it = this->mActors.begin(); it != this->mActors.end(); it++)
        {
            mRenderer->RemoveActor(it->second);
        }
        
        this->mActors.clear();
        this->update();
    }
}

void SceneWidget::zoomFit()
{
    // Zoom to extent of all actors
    vtkSmartPointer<vtkActor> actor = mRenderer->GetActors()->GetLastActor();
    if (actor != nullptr) {
        double bounds[6];
        actor->GetBounds(bounds);
        for (auto it = this->mActors.begin(); it != this->mActors.end(); it++)
        {
            double actorBounds[6];
            it->second->GetBounds(actorBounds);
            bounds[0] = std::min(bounds[0], actorBounds[0]);
            bounds[2] = std::min(bounds[2], actorBounds[2]);
            bounds[4] = std::min(bounds[4], actorBounds[4]);
            bounds[1] = std::max(bounds[1], actorBounds[1]);
            bounds[3] = std::max(bounds[3], actorBounds[3]);
            bounds[5] = std::max(bounds[5], actorBounds[5]);
        }
        mRenderer->ResetCamera(bounds);
    }

    renderWindow()->Render();
}

void SceneWidget::zoomBox(const Eigen::AlignedBox3f & box)
{
    double bounds[6];
    bounds[0] = box.min()[0];
    bounds[2] = box.min()[1];
    bounds[4] = box.min()[2];
    bounds[1] = box.max()[0];
    bounds[3] = box.max()[1];
    bounds[5] = box.max()[2];
    mRenderer->ResetCamera(bounds);

    renderWindow()->Render();
}
