#include <vtkPointData.h>
#include <vtkImageData.h>
#include <vtkImageStencil.h>
#include <vtkPolyDataToImageStencil.h>
#include <vtkPLYReader.h>
#include <vtkLinearSubdivisionFilter.h>
#include <vtkAdaptiveSubdivisionFilter.h>
#include <vtkIdList.h>
#include <vtkUnsignedCharArray.h>

#include <vtkPolyData.h>
#include <vtkProperty.h>
#include <vtkTriangle.h>
#include <vtkPointData.h>
#include <vtkCellData.h>
#include <vtkProperty.h>

#include "catheter.h"

Catheter::Catheter(const std::string& modelPath)
    :mModelPath(modelPath), mPolyData(vtkSmartPointer<vtkPolyData>::New())
{
    /*
    vtkSmartPointer<vtkPLYReader> reader = vtkSmartPointer<vtkPLYReader>::New();
    reader->SetFileName(modelPath.c_str());
    reader->Update();

    this->mPolyData = reader->GetOutput();

    */

    //RGB Colors
    unsigned char vectorColor[3] = { 0,0,0 };
    vtkSmartPointer<vtkUnsignedCharArray>pointsColors = vtkSmartPointer<vtkUnsignedCharArray>::New();
    pointsColors->SetNumberOfComponents(3);
    vtkSmartPointer<vtkPoints>points = vtkSmartPointer<vtkPoints>::New();
    vtkSmartPointer<vtkTriangle>triangle = vtkSmartPointer<vtkTriangle>::New();
    vtkPolyData* polydata = vtkPolyData::New();
    vtkSmartPointer<vtkCellArray>triangles = vtkSmartPointer<vtkCellArray>::New();

    //for x y z
    std::ifstream filestream(modelPath.c_str()); //
    std::string line;
    unsigned int N, x0, y0, z0, vectorIndex = 0, COUNT;
    unsigned int vector1R, vector1G, vector1B;

    // int sum=0;
    int index = 0;
    int Count = 0;
    unsigned int headCount = 14;
    std::string str1, str2, str3;
    int vectorCount = 0, meshCount = 0;
    int temp = 0;

    // std::stringstream linestream;
    while (std::getline(filestream, line))  //read whole line
    {
        if (line.empty()) continue;
        index = index + 1;
        std::stringstream linestream;
        linestream << line;
        // linestream >>str1>>str2 >>vectorCount;
        if (index <= headCount)
        {
            //    {
          //     linestream >>str1;
          //   if((index==1)&&(str1!="ply"))
          //     {
          //       break;
              // {
            if (index == 4)
            {
                linestream >> str1 >> str2 >> temp;
                if ((str1 == "element") && (str2 == "vertex"))
                {
                    vectorCount = temp;
                }
            }
            if (index == 8)
            {
                linestream >> str1 >> str2 >> temp;
                if ((str1 == "element") && (str2 == "face"))
                {
                    meshCount = temp;
                }
            }
        }//index <=headCount
        else
        {
            if ((index > 14) && (index <= (vectorCount + headCount)))
            {
                float x, y, z;
                linestream >> x >> y >> z;
                temp = points->InsertNextPoint(x, y, z);
                //  
            }
            else
            {
                unsigned int N, COUNT;
                linestream >> N >> x0 >> y0 >> z0 >> vector1R >> vector1G >> vector1B >> COUNT;
                Count = Count + 1;
                //Color
                vectorColor[0] = vector1R;
                vectorColor[1] = vector1G;
                vectorColor[2] = vector1B;
                //triangle
                triangle->GetPointIds()->SetId(0, x0);
                triangle->GetPointIds()->SetId(1, y0);
                triangle->GetPointIds()->SetId(2, z0);
                triangles->InsertNextCell(triangle);
                //insert Color 
                pointsColors->InsertNextTypedTuple(vectorColor);
            }
        }
    }
    mPoints = points;
    this->mPolyData->SetPoints(points);
    this->mPolyData->SetPolys(triangles);
    this->mPolyData->GetCellData()->SetScalars(pointsColors);

}

Catheter::~Catheter()
{
    this->mPolyData = nullptr;
}

vtkSmartPointer<vtkPoints> Catheter::voxelize(float spacing)
{
    vtkSmartPointer<vtkAdaptiveSubdivisionFilter> subdivider = vtkSmartPointer<vtkAdaptiveSubdivisionFilter>::New();
    subdivider->SetInputData(this->mPolyData);
    subdivider->SetMaximumEdgeLength(spacing);
    subdivider->Update();
    vtkSmartPointer<vtkPoints> points = subdivider->GetOutput()->GetPoints();

    return points;
}
