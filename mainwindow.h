#ifndef MAINWINDOW_H
#define MAIN<PERSON>NDOW_H

#include <array>
#include <vector>
#ifndef EIGEN_USE_BLAS
    #define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>
#include <QMainWindow>

#include "reconstructor.h"
#include "SceneWidget.h"
#include "NDIDriver.h"
#include "catheter_visualizer.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget* parent = 0);
    ~MainWindow();

    void clear();

protected:
    void openRespiration();

    void openScanData();
    void openCatheterModel();
    void startScan();    
    void endScan();
    void updateScene();
    void save();
    void resolutionChanged(int currentIdx);
    void respThresholdChanged(int value);
    void globalMeshOpacityChanged(int value);
    void toggleVisibility(int state);
    
    void addRespSignal(double timestamp, float resp);

    void pause();

    SceneWidget* sceneWidget();

protected:
    void closeEvent(QCloseEvent* ev);
private:
    void simulateScanWithData();
    void emitRespiration();
    void scanWithNDIDevice();
    NDIDriver* mNDIDriver;

    Ui::MainWindow* mUI;
    FAMReconstructor::Ptr mReconstructor;
    int mRespSamplingRate;
    float mRespThreshold;
    std::vector<float> mPredefinedResolutions;
    Catheter::Ptr mCatheter;

   //
    Catheter::Ptr mCatheterDisplay;

    std::chrono::steady_clock::time_point mStartTimeStamp;
    std::vector<float> mRawRespiration;
    std::vector<std::tuple<Eigen::Vector3f, Eigen::Quaternionf, int>> mRawScan;
    Eigen::AlignedBox3f mBounds;

    std::recursive_timed_mutex mMutex;
    bool mStopEmitRespiration;
    bool mFinished;
    bool mFinishing;
    double mRespLastUpdateTimestamp;

    vtkSmartPointer<vtkActor> mCatheterActor;
  
    vtkSmartPointer<vtkActor> mLocalActor;
    vtkSmartPointer<vtkActor> mTransitionActor;
    vtkSmartPointer<vtkActor> mGlobalActor;
    vtkSmartPointer<vtkPolyData> mLocalMesh;
    vtkSmartPointer<vtkPolyData> mTransitionMesh;
    vtkSmartPointer<vtkPolyData> mGlobalMesh;
    
    // 导管点可视化相关 - 使用新的线程安全可视化管理器
    std::unique_ptr<CatheterVisualizer> mCatheterVisualizer;

    std::chrono::steady_clock::time_point mCatheterLastUpdateTime;
    double mCatheterUpdateFPS;

    std::chrono::steady_clock::time_point mLocalMeshLastUpdateTime;
    double mLocalMeshUpdateFPS;

    std::chrono::steady_clock::time_point mGlobalMeshLastUpdateTime;
    double mGlobalMeshUpdateFPS;

    double mGlobalMeshOpacity;
    bool mShowLocalMesh;
    bool mShowTransitionMesh;

    //static int  mNDIFlag;


};

#endif // MAINWINDOW_H

