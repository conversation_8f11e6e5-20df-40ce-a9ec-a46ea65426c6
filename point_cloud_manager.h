#ifndef POINT_CLOUD_MANAGER_H
#define POINT_CLOUD_MANAGER_H

#include <vector>
#include <mutex>
#include <atomic>
#include <memory>
#include <chrono>
#include <unordered_set>

#ifndef EIGEN_USE_BLAS
#define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>

/**
 * @brief 线程安全的点云数据管理器
 * 
 * 专门用于管理导管点云数据的累积、去重和内存优化
 * 主要功能：
 * 1. 线程安全的点云数据存储
 * 2. 自动去重避免重复点
 * 3. 内存使用优化
 * 4. 高效的数据访问接口
 */
class PointCloudManager
{
public:
    /**
     * @brief 构造函数
     * @param maxPoints 最大存储点数，0表示无限制
     * @param spatialTolerance 空间容差，用于去重
     */
    explicit PointCloudManager(size_t maxPoints = 0, float spatialTolerance = 0.1f);
    
    ~PointCloudManager();

    /**
     * @brief 添加新的点云数据
     * @param points 要添加的点坐标向量
     * @param clearCurrent 是否清空当前帧数据
     */
    void addPoints(const std::vector<Eigen::Vector3f>& points, bool clearCurrent = true);

    /**
     * @brief 获取当前帧的点云数据
     * @param points 输出的点坐标向量
     * @return 是否有新数据
     */
    bool getCurrentPoints(std::vector<Eigen::Vector3f>& points) const;

    /**
     * @brief 获取所有累积的点云数据
     * @param points 输出的点坐标向量
     * @return 是否有新数据
     */
    bool getAllPoints(std::vector<Eigen::Vector3f>& points) const;

    /**
     * @brief 清空所有数据
     */
    void clear();

    /**
     * @brief 获取当前帧点数
     */
    size_t getCurrentPointCount() const;

    /**
     * @brief 获取累积总点数
     */
    size_t getTotalPointCount() const;

    /**
     * @brief 获取内存使用情况（字节）
     */
    size_t getMemoryUsage() const;

    /**
     * @brief 设置最大点数限制
     * @param maxPoints 最大点数，0表示无限制
     */
    void setMaxPoints(size_t maxPoints);

    /**
     * @brief 设置空间容差
     * @param tolerance 空间容差
     */
    void setSpatialTolerance(float tolerance);

    /**
     * @brief 获取性能统计信息
     */
    struct PerformanceStats {
        size_t totalAddOperations;
        size_t duplicatePointsFiltered;
        std::chrono::duration<double, std::milli> averageAddTime;
        std::chrono::duration<double, std::milli> averageGetTime;
    };
    
    PerformanceStats getPerformanceStats() const;

private:
    /**
     * @brief 点的哈希函数，用于快速去重
     */
    struct PointHash {
        size_t operator()(const Eigen::Vector3f& point) const;
        float tolerance;
        explicit PointHash(float tol) : tolerance(tol) {}
    };

    /**
     * @brief 点的相等比较函数
     */
    struct PointEqual {
        bool operator()(const Eigen::Vector3f& a, const Eigen::Vector3f& b) const;
        float tolerance;
        explicit PointEqual(float tol) : tolerance(tol) {}
    };

    /**
     * @brief 应用内存限制，移除最旧的点
     */
    void enforceMemoryLimit();

    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats(std::chrono::steady_clock::time_point startTime, 
                               bool isAddOperation, size_t duplicatesFiltered = 0);

private:
    // 数据存储
    std::vector<Eigen::Vector3f> mCurrentPoints;
    std::vector<Eigen::Vector3f> mAllPoints;
    std::unordered_set<Eigen::Vector3f, PointHash, PointEqual> mPointSet;
    
    // 线程安全
    mutable std::mutex mCurrentMutex;
    mutable std::mutex mAllMutex;
    mutable std::mutex mStatsMutex;
    
    // 配置参数
    std::atomic<size_t> mMaxPoints;
    std::atomic<float> mSpatialTolerance;
    
    // 状态标志
    mutable std::atomic<bool> mHasNewCurrentData;
    mutable std::atomic<bool> mHasNewAllData;
    
    // 性能统计
    PerformanceStats mStats;
    std::chrono::steady_clock::time_point mLastStatsUpdate;
    
    // 内存管理
    std::atomic<size_t> mCurrentMemoryUsage;
    static constexpr size_t POINT_SIZE_BYTES = sizeof(Eigen::Vector3f);
};

#endif // POINT_CLOUD_MANAGER_H
