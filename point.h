#ifndef FAMPOINT_H
#define FAMPOINT_H

#ifndef EIGEN_USE_BLAS
    #define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>
#include <memory>

class FAMPoint
{
public:
    Eigen::Vector3f catheterCoord;
    Eigen::Quaternionf catheterQuaternion;
    int LAT = 0;
    double timestamp= 0.0;
   
    Eigen::Isometry3f transform() const
    {
        Eigen::Isometry3f iso3(catheterQuaternion);
        iso3.pretranslate(catheterCoord);
        return iso3;
    }

    FAMPoint interpolate(float t, const FAMPoint& other) const
    {
        FAMPoint ret;
        ret.catheterCoord = this->catheterCoord + (other.catheterCoord - this->catheterCoord) * t;
        ret.catheterQuaternion = this->catheterQuaternion.slerp(t, other.catheterQuaternion);
        ret.LAT = this->LAT + (other.LAT - this->LAT) * t;
        ret.timestamp = this->timestamp + (other.timestamp - this->timestamp) * t;
        return ret;
    }
};

#endif // !FAMPOINT_H
