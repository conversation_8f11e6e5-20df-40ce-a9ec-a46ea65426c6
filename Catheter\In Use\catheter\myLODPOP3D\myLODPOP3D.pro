QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h


LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkFiltersSources-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingCore-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingQt-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonCore-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonExecutionModel-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkDICOMParser-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonDataModel-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkIOImage-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkFiltersCore-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingOpenGL2-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkFiltersModeling-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonMisc-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkFiltersGeneral-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonExecutionModel-8.2.lib

LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkImagingHybrid-8.2.lib

LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkfreetype-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingFreeType-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonColor-8.2.lib

LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkIOLegacy-8.2.lib


LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingVolumeOpenGL2-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingOpenGL2-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingQt-8.2.lib


LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkCommonTransforms-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkFiltersGeneric-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingAnnotation-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkInteractionWidgets-8.2.lib
LIBS +=D:/3D/VTK/VTK_8.2/VTK_prefix/lib/vtkRenderingVolume-8.2.lib






# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

win32:CONFIG(release, debug|release): LIBS += -L$$PWD/../../../VTK_8.2/VTK_prefix/lib/ -lvtkInteractionStyle-8.2
else:win32:CONFIG(debug, debug|release): LIBS += -L$$PWD/../../../VTK_8.2/VTK_prefix/lib/ -lvtkInteractionStyle-8.2

INCLUDEPATH += $$PWD/../../../VTK_8.2/VTK_prefix/include/vtk-8.2
DEPENDPATH += $$PWD/../../../VTK_8.2/VTK_prefix/include/vtk-8.2

# Default rules for deployment.
#qnx: target.path = /tmp/$${TARGET}/bin
#else: unix:!android: target.path = /opt/$${TARGET}/bin
#!isEmpty(target.path): INSTALLS += target
