#include "point_cloud_manager.h"
#include <algorithm>
#include <cmath>

PointCloudManager::PointCloudManager(size_t maxPoints, float spatialTolerance)
    : mPointSet(0, PointHash(spatialTolerance), PointEqual(spatialTolerance))
    , mMaxPoints(maxPoints)
    , mSpatialTolerance(spatialTolerance)
    , mHasNewCurrentData(false)
    , mHasNewAllData(false)
    , mCurrentMemoryUsage(0)
{
    mStats.totalAddOperations = 0;
    mStats.duplicatePointsFiltered = 0;
    mStats.averageAddTime = std::chrono::duration<double, std::milli>(0);
    mStats.averageGetTime = std::chrono::duration<double, std::milli>(0);
    mLastStatsUpdate = std::chrono::steady_clock::now();
}

PointCloudManager::~PointCloudManager()
{
    clear();
}

void PointCloudManager::addPoints(const std::vector<Eigen::Vector3f>& points, bool clearCurrent)
{
    auto startTime = std::chrono::steady_clock::now();
    size_t duplicatesFiltered = 0;
    
    // 更新当前帧数据
    {
        std::lock_guard<std::mutex> lock(mCurrentMutex);
        if (clearCurrent) {
            mCurrentPoints.clear();
        }
        mCurrentPoints.reserve(mCurrentPoints.size() + points.size());
        
        for (const auto& point : points) {
            mCurrentPoints.push_back(point);
        }
        mHasNewCurrentData = true;
    }
    
    // 更新累积数据（带去重）
    {
        std::lock_guard<std::mutex> lock(mAllMutex);
        
        for (const auto& point : points) {
            // 检查是否为重复点
            if (mPointSet.find(point) == mPointSet.end()) {
                mAllPoints.push_back(point);
                mPointSet.insert(point);
                mCurrentMemoryUsage += POINT_SIZE_BYTES;
            } else {
                duplicatesFiltered++;
            }
        }
        
        // 应用内存限制
        if (mMaxPoints > 0 && mAllPoints.size() > mMaxPoints) {
            enforceMemoryLimit();
        }
        
        mHasNewAllData = true;
    }
    
    updatePerformanceStats(startTime, true, duplicatesFiltered);
}

bool PointCloudManager::getCurrentPoints(std::vector<Eigen::Vector3f>& points) const
{
    auto startTime = std::chrono::steady_clock::now();
    
    std::lock_guard<std::mutex> lock(mCurrentMutex);
    if (!mHasNewCurrentData) {
        return false;
    }
    
    points = mCurrentPoints;
    mHasNewCurrentData = false;
    
    const_cast<PointCloudManager*>(this)->updatePerformanceStats(startTime, false);
    return true;
}

bool PointCloudManager::getAllPoints(std::vector<Eigen::Vector3f>& points) const
{
    auto startTime = std::chrono::steady_clock::now();
    
    std::lock_guard<std::mutex> lock(mAllMutex);
    if (!mHasNewAllData) {
        return false;
    }
    
    points = mAllPoints;
    mHasNewAllData = false;
    
    const_cast<PointCloudManager*>(this)->updatePerformanceStats(startTime, false);
    return true;
}

void PointCloudManager::clear()
{
    {
        std::lock_guard<std::mutex> lock(mCurrentMutex);
        mCurrentPoints.clear();
        mCurrentPoints.shrink_to_fit();
        mHasNewCurrentData = false;
    }
    
    {
        std::lock_guard<std::mutex> lock(mAllMutex);
        mAllPoints.clear();
        mAllPoints.shrink_to_fit();
        mPointSet.clear();
        mHasNewAllData = false;
        mCurrentMemoryUsage = 0;
    }
}

size_t PointCloudManager::getCurrentPointCount() const
{
    std::lock_guard<std::mutex> lock(mCurrentMutex);
    return mCurrentPoints.size();
}

size_t PointCloudManager::getTotalPointCount() const
{
    std::lock_guard<std::mutex> lock(mAllMutex);
    return mAllPoints.size();
}

size_t PointCloudManager::getMemoryUsage() const
{
    return mCurrentMemoryUsage.load();
}

void PointCloudManager::setMaxPoints(size_t maxPoints)
{
    mMaxPoints = maxPoints;
    
    // 如果当前点数超过新限制，立即应用
    if (maxPoints > 0) {
        std::lock_guard<std::mutex> lock(mAllMutex);
        if (mAllPoints.size() > maxPoints) {
            enforceMemoryLimit();
        }
    }
}

void PointCloudManager::setSpatialTolerance(float tolerance)
{
    mSpatialTolerance = tolerance;
    
    // 重建哈希集合以使用新的容差
    std::lock_guard<std::mutex> lock(mAllMutex);
    mPointSet = std::unordered_set<Eigen::Vector3f, PointHash, PointEqual>(
        0, PointHash(tolerance), PointEqual(tolerance));
    
    // 重新插入所有点
    for (const auto& point : mAllPoints) {
        mPointSet.insert(point);
    }
}

PointCloudManager::PerformanceStats PointCloudManager::getPerformanceStats() const
{
    std::lock_guard<std::mutex> lock(mStatsMutex);
    return mStats;
}

void PointCloudManager::enforceMemoryLimit()
{
    size_t maxPoints = mMaxPoints.load();
    if (maxPoints == 0 || mAllPoints.size() <= maxPoints) {
        return;
    }
    
    size_t pointsToRemove = mAllPoints.size() - maxPoints;
    
    // 移除最旧的点（从前面开始）
    auto removeEnd = mAllPoints.begin() + pointsToRemove;
    
    // 从哈希集合中移除这些点
    for (auto it = mAllPoints.begin(); it != removeEnd; ++it) {
        mPointSet.erase(*it);
        mCurrentMemoryUsage -= POINT_SIZE_BYTES;
    }
    
    // 从向量中移除
    mAllPoints.erase(mAllPoints.begin(), removeEnd);
}

void PointCloudManager::updatePerformanceStats(std::chrono::steady_clock::time_point startTime, 
                                              bool isAddOperation, size_t duplicatesFiltered)
{
    auto endTime = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::duration<double, std::milli>>(endTime - startTime);
    
    std::lock_guard<std::mutex> lock(mStatsMutex);
    
    if (isAddOperation) {
        mStats.totalAddOperations++;
        mStats.duplicatePointsFiltered += duplicatesFiltered;
        
        // 计算移动平均
        if (mStats.totalAddOperations == 1) {
            mStats.averageAddTime = duration;
        } else {
            double alpha = 0.1; // 平滑因子
            mStats.averageAddTime = std::chrono::duration<double, std::milli>(
                alpha * duration.count() + (1.0 - alpha) * mStats.averageAddTime.count());
        }
    } else {
        // 更新获取操作的平均时间
        static size_t getOperationCount = 0;
        getOperationCount++;
        
        if (getOperationCount == 1) {
            mStats.averageGetTime = duration;
        } else {
            double alpha = 0.1;
            mStats.averageGetTime = std::chrono::duration<double, std::milli>(
                alpha * duration.count() + (1.0 - alpha) * mStats.averageGetTime.count());
        }
    }
    
    mLastStatsUpdate = endTime;
}

size_t PointCloudManager::PointHash::operator()(const Eigen::Vector3f& point) const
{
    // 将点坐标量化到容差网格
    int x = static_cast<int>(std::round(point.x() / tolerance));
    int y = static_cast<int>(std::round(point.y() / tolerance));
    int z = static_cast<int>(std::round(point.z() / tolerance));
    
    // 简单的哈希组合
    size_t h1 = std::hash<int>{}(x);
    size_t h2 = std::hash<int>{}(y);
    size_t h3 = std::hash<int>{}(z);
    
    return h1 ^ (h2 << 1) ^ (h3 << 2);
}

bool PointCloudManager::PointEqual::operator()(const Eigen::Vector3f& a, const Eigen::Vector3f& b) const
{
    return (a - b).norm() < tolerance;
}
