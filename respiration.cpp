#include "respiration.h"

Respiration::Respiration(int samplingRate)
    : mSamplingRate(samplingRate)
    , mToBeIndexed(0)
{
    this->mIndex.reset(new LUT1D(1, *this, { 100 }));
}

Respiration::~Respiration()
{
}

void Respiration::addSample(float resp, double timestamp)
{
    Respiration::Sample sample;
    sample.resp = resp;
    sample.timestamp = timestamp;
    sample.phase = -1.0f;
    this->mData.push_back(sample);
    if (this->mData.size() - this->mToBeIndexed >= this->minNumToUpdate())
    {
        if (this->mLock.try_lock())
        {
            this->mIndex->addPoints(this->mToBeIndexed, this->mData.size() - 1);
            this->estimatePhase(this->mToBeIndexed, this->mData.size());
            this->mToBeIndexed = this->mData.size();
            this->mLock.unlock();
        }
    }
}

const Respiration::Sample& Respiration::lookup(double timestamp)
{
    std::lock_guard<std::recursive_timed_mutex> lock(this->mLock);
    size_t retIndex = this->mToBeIndexed - 1;
    if (this->mData[retIndex].timestamp < timestamp)
        return this->mData[retIndex];

    double outDist = 0.0;
    nanoflann::KNNResultSet<double> resultSet(1);
    resultSet.init(&retIndex, &outDist);
    this->mIndex->findNeighbors(resultSet, &timestamp, {});
    return this->mData[retIndex];
}

void Respiration::estimatePhase(size_t start, size_t end)
{
    for (size_t i = start; i < end; i++)
    {
        // fake phase
        this->mData[i].phase = 0.0f;
    }
}

void Respiration::clear()
{
    this->mData.clear();
    this->mToBeIndexed = 0;
    this->mIndex.reset(new LUT1D(1, *this, { 100 }));
}
