#include <QFileDialog>
#include <QFile>
#include <QTextStream.h>
#include <QtWidgets/QMainWindow>

#include <math.h>
#include <thread>
#include <chrono>
#include <random>

#include <vtkMatrix4x4.h>
#include <vtkMapper.h>
#include <vtkProperty.h>
#include <vtkPLYWriter.h>
#include <vtkImageData.h>
#include <vtkVolumeMapper.h>
#include <vtkNIFTIImageWriter.h>
#include <vtkRenderWindow.h>
#include <vtkPoints.h>
#include <vtkVertexGlyphFilter.h>
#include <vtkPolyData.h>
#include <vtkUnsignedCharArray.h>
#include <vtkPointData.h>

#include <pthread.h>
#include "RespEmitter.h"

#include "ui_mainwindow.h"
#include "volume.h"
#include "reconstructor.h"
#include "catheter_visualizer.h"
#include "mainwindow.h"


//#include <vtkGPUVolumeRayCastMapper.h>
#include <vtkGPUVolumeRayCastMapper.h>
#include <vtkFixedPointVolumeRayCastMapper.h>
#include <vtkGenericOpenGLRenderWindow.h>
#include <vtkProperty.h>
#include <vtkRenderWindowInteractor.h>


#define  VOLUME_MAX				1000
#define  DELTA					0.1
float  tempp;
Eigen::Vector3f halfSizepp;
MainWindow::MainWindow(QWidget* parent)
	: QMainWindow(parent)
	, mUI(new Ui::MainWindow)
	, mStopEmitRespiration(false)
	, mFinished(false)
	, mFinishing(false)
	, mGlobalMeshUpdateFPS(0.0)
	, mNDIDriver(nullptr)
	, mGlobalMeshOpacity(1.0)
	, mShowLocalMesh(true)
	, mShowTransitionMesh(true)
	, mCatheterDisplay(nullptr)
{
	mUI->setupUi(this);
	this->mReconstructor = nullptr;
	
	auto availableResolutions = QStringList({
		//"0.5",
		"1.0", "1.5", "2.0", "2.5", "3.0", "3.5", "4.0", "4.5",
		"5.0", "5.5", "6.0", "6.5", "7.0", "7.5", "8.0", "8.5", "9.0"});

	for (int i = 0; i < availableResolutions.size(); i++)
	{
		mPredefinedResolutions.push_back(availableResolutions[i].toFloat());
	}
	this->mUI->mResolutionComboBox->addItems(availableResolutions);

	connect(mUI->mOpenRespBtn, &QPushButton::clicked, this, &MainWindow::openRespiration);
	connect(mUI->mOpenCatheterBtn, &QPushButton::clicked, this, &MainWindow::openCatheterModel);
	connect(mUI->mOpenScanBtn, &QPushButton::clicked, this, &MainWindow::openScanData);
	connect(mUI->mStartSimulatingBtn, &QPushButton::clicked, this, &MainWindow::startScan);
	connect(mUI->mSaveBtn, &QPushButton::clicked, this, &MainWindow::save);
	connect(mUI->mZoomFitBtn, &QPushButton::clicked, this->sceneWidget(), &SceneWidget::zoomFit);
	connect(mUI->mResolutionComboBox, QOverload<int>::of(&QComboBox::activated), this, &MainWindow::resolutionChanged);
	connect(mUI->mRespThresholdSlider, &QSlider::valueChanged, this, &MainWindow::respThresholdChanged);
	connect(mUI->mGlobalMeshOpacitySlider, &QSlider::valueChanged, this, &MainWindow::globalMeshOpacityChanged);
	connect(mUI->mLocalMeshCheckBox, &QCheckBox::stateChanged, this, &MainWindow::toggleVisibility);
	connect(mUI->mTransitionMeshCheckBox, &QCheckBox::stateChanged, this, &MainWindow::toggleVisibility);
	connect(mUI->mClearButton, &QPushButton::clicked, this, &MainWindow::clear);
	connect(mUI->mPauseBtn, &QPushButton::clicked, this, &MainWindow::pause);
	this->mUI->mResolutionComboBox->setCurrentIndex(0);//2
	this->mUI->mRespThresholdSlider->setValue(100);
	mUI->mGlobalMeshOpacitySlider->setValue(10);

	// 初始化导管可视化管理器
	mCatheterVisualizer = std::make_unique<CatheterVisualizer>(this->sceneWidget(), this);

	// 设置点云属性
	mCatheterVisualizer->setPointCloudProperties(
		3.0f,  // 点大小
		Eigen::Vector4f(0.0f, 1.0f, 0.0f, 1.0f),  // 绿色
		1.0f   // 不透明
	);

	// 设置最大显示点数以控制性能（可选）
	mCatheterVisualizer->setMaxDisplayPoints(50000);  // 最多显示5万个点
}

static int  mNDIFlag = 0;
MainWindow::~MainWindow()
{
	// 停止可视化管理器
	if (mCatheterVisualizer) {
		mCatheterVisualizer->stopVisualization();
		mCatheterVisualizer->clearVisualization();
	}

	this->clear();
	this->mCatheter = nullptr;
	delete this->mUI;
	this->mUI = nullptr;
}

void MainWindow::clear()
{
	this->sceneWidget()->clear();
	{
		std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
		if (this->mReconstructor && !this->mReconstructor->isFinished())
			this->mReconstructor->completeReconstruction();

		this->mReconstructor = nullptr;
	}
	
	this->mLocalActor = nullptr;
	this->mLocalMesh = nullptr;
	this->mTransitionActor = nullptr;
	this->mTransitionMesh = nullptr;
	this->mGlobalActor = nullptr;
	this->mGlobalMesh = nullptr;
	
	// 清除导管点可视化
	if (mCatheterVisualizer) {
		mCatheterVisualizer->stopVisualization();
		mCatheterVisualizer->clearVisualization();
	}

	this->mStopEmitRespiration = false;
	this->mFinished = false;
	this->mFinishing = false;
}

//
void MainWindow::pause()
{
	if(!mNDIFlag)
	{
	  mNDIFlag = true;
	  this->mUI->mPauseBtn->setText("ReStart"); 
	}
	else
	if(mNDIFlag ==true)
	{
	  mNDIFlag = false;
	  this->mUI->mPauseBtn->setText("Pause");

	}
}

void MainWindow::openRespiration()
{
	QString filename = QFileDialog::getOpenFileName(this,
		"Open Respiration Data", "../../data", "Respiration Data (*.txt)");

	if (filename.isEmpty()) return;

	QFile respFile(filename);
	if (respFile.open(QIODevice::ReadOnly))
	{
		this->mRawRespiration.clear();
		QTextStream textStream(&respFile);
		float buffer[2];
		while (!textStream.atEnd())
		{
			QString line = textStream.readLine();
			if (line.isEmpty()) continue;
			QStringList splits = line.split(" ");
			assert(splits.count() <= 2);
			for (int i = 0; i < splits.count(); i++)
			{
				buffer[i] = splits[i].toFloat();
			}

			this->mRawRespiration.push_back(buffer[0]);
		}

		respFile.close();
	}
}

void MainWindow::openScanData()
{
	QString filename = QFileDialog::getOpenFileName(this,
		"Open Scan Data", "../../data", "Scan Data (*.txt)");

	if (filename.isEmpty()) return;

	QFile scanFile(filename);
	if (scanFile.open(QIODevice::ReadOnly))
	{
		this->mRawScan.clear();
		this->mBounds.setEmpty();
		QTextStream textStream(&scanFile);
		float buffer[8];
		while (!textStream.atEnd())
		{
			QString line = textStream.readLine();
			if (line.isEmpty()) continue;
			QStringList splits = line.split(" ");
			int spacingId = -1;
			
			for (int i = 0; i < splits.count(); i++)
			{
				buffer[i] = splits[i].toFloat();
			}

			Eigen::Vector3f coord(buffer[4], buffer[5], buffer[6]);
			Eigen::Quaternionf quat(buffer[0], buffer[1], buffer[2], buffer[3]);
			if (splits.count() == 8)
				spacingId = (int)buffer[7];

			this->mRawScan.push_back(std::tuple<Eigen::Vector3f, Eigen::Quaternionf, int>(
				coord, quat, spacingId));
			this->mBounds.extend(coord);
		}

		scanFile.close();
		this->mReconstructor.reset();
	}
}

void MainWindow::openCatheterModel()
{
	QString filename = QFileDialog::getOpenFileName(this,
		"Open Catheter Model", "../../catheter", "Catheter Model File (*.ply)");//ply
	
	if (filename.isEmpty()) return;

	this->mCatheter.reset(new Catheter(filename.toStdString()));

	// full length catheter algorithm
	QString filename1 = QFileDialog::getOpenFileName(this,
		"Open Catheter Model", "../../catheter", "Catheter Model File (*.ply)");//ply

	if (filename1.isEmpty()) return;

	//this->mCatheter.reset(new Catheter(filename1.toStdString()));
	this->mCatheterDisplay.reset(new Catheter(filename1.toStdString()));

	//this->mCatheterDisplay = this->mCatheter;
}

void MainWindow::startScan()
{
	if (!this->mCatheter)
	{
		this->mUI->mStatusbar->showMessage("Must select a catheter model.");
		return;
	}

	this->mUI->mStatusbar->showMessage("Start Scan ...");
	this->clear();	
	QApplication::processEvents();

	//this->mCatheterActor = this->sceneWidget()->addDataSet(this->mCatheter->polyData(), "catheter", Eigen::Vector4d(1.0, 0.0, 0.0, 1.0));// 0.0,1.0,0.0

	this->mCatheterActor = this->sceneWidget()->addDataSet(this->mCatheterDisplay->polyData(), "catheter", Eigen::Vector4d(1.0, 0.0, 0.0, 1.0));// 0.0,1.0,0.0

	this->mRespSamplingRate = this->mUI->mSamplingRateSpinBox->value();
	this->mStopEmitRespiration = false;
	this->mFinished = false;
	this->mFinishing = false;

	this->mUI->mRespView->addGraph();
	this->mUI->mRespView->graph(0)->setPen(QPen(QColor(40, 110, 255)));
	this->mUI->mRespView->addGraph();
	this->mUI->mRespView->graph(1)->setPen(QPen(QColor(255, 0, 0)));
	this->mUI->mRespView->yAxis->setRange(-10, 10);

	std::thread emitRespirationThread(&MainWindow::emitRespiration, this);
	emitRespirationThread.detach();
	//let respiration run first
	std::this_thread::sleep_for(std::chrono::milliseconds(1000));

	int currentRespCount = 0;

	this->mStartTimeStamp = std::chrono::steady_clock::now();

	// 启动导管点云可视化
	if (mCatheterVisualizer && this->mReconstructor) {
		mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
		mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
	}

	if (this->mRawScan.size() == 0)
	{
		scanWithNDIDevice();
	}
	else
	{
		simulateScanWithData();
	}

	{
		std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
		this->mStopEmitRespiration = true;
	}

	this->mUI->mStatusbar->showMessage("Simulating Scan 100%");

	while (true)
	{
		{
			std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
			if (this->mFinished) break;
		}

		this->sceneWidget()->update();
		QApplication::processEvents();
		std::this_thread::sleep_for(std::chrono::milliseconds(1));
	}


	QString volumeFilename = QFileDialog::getSaveFileName(this,
		"Save Volume", "./image", "Nifti Format (*.nii)");

	if (!volumeFilename.isEmpty())
	{
		vtkSmartPointer<vtkNIFTIImageWriter> writer = vtkSmartPointer<vtkNIFTIImageWriter>::New();
		std::string str = volumeFilename.toStdString();
		writer->SetFileName(str.c_str());
		writer->SetInputData(this->mReconstructor->baseVolume()->image());
		writer->Write();
		writer->Update();
	}

	//vtkImageData* dataSet = this->mReconstructor->baseVolume()->image();
	//this->sceneWidget()->addVolume(this->mReconstructor->baseVolume()->image(), "catheter");
	

	{
		std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
		this->mReconstructor->completeReconstruction();
		this->mReconstructor = nullptr;
	}
	this->mUI->mStatusbar->showMessage("Reconstruction Completed.");
	QApplication::processEvents();	
}

void MainWindow::scanWithNDIDevice()
{
	this->mRawScan.clear();

	this->mNDIDriver = new NDIDriver("COM3");
	this->mNDIDriver->on_error([&](const std::string& msg)
	{
		this->mUI->mStatusbar->showMessage(QString::fromStdString(msg));
	});	

	{
		Eigen::AlignedBox3f bounds2;
		float halfScanSize = this->mUI->mScanSizeSpinBox->value() / 8.0f;
		tempp = halfScanSize;
		Eigen::Vector3f halfSize(halfScanSize, halfScanSize, halfScanSize);
		bounds2.extend(halfSize);
		bounds2.extend(-halfSize);
		this->sceneWidget()->zoomBox(bounds2);
	}

    while (!mNDIDriver->set_tracking_status(true)) _sleep(10);

	disconnect(mUI->mStartSimulatingBtn, &QPushButton::clicked, this, &MainWindow::startScan);
	connect(mUI->mStartSimulatingBtn, &QPushButton::clicked, this, &MainWindow::endScan);
	this->mUI->mStartSimulatingBtn->setText("End Scan");
	QApplication::processEvents();

	vtkSmartPointer<vtkMatrix4x4> userTransformMatrix = vtkSmartPointer<vtkMatrix4x4>::New();
	this->mCatheterLastUpdateTime = std::chrono::steady_clock::now();
	double timestampBias = -1.0;
	double sampleInterval = this->mNDIDriver->get_sample_interval().count();
	long long sampleCount = 0;
	while (!mFinishing) {
		static float lastTx = 0, lastTy = 0, lastTz = 0;
		if (auto data = mNDIDriver->get_data(); data.has_value())
		{
			auto& items = data.value();
			sampleCount++;
			double ndiTimestamp = sampleCount * sampleInterval;// item.timespec_s * 1000.0 + item.timespec_ns / 1000.0;

			const std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mStartTimeStamp;
			double localTimestamp = elapsed.count();
			if (timestampBias < 0)
			{
				timestampBias = localTimestamp - ndiTimestamp;
			}
			else
			{
				double tempBias = localTimestamp - ndiTimestamp;
				if (timestampBias - tempBias > 100)
					continue;
			}


/*
			//pause function    added by yang 29/05 2024
			if (mNDIFlag == 2)
			{
				std::this_thread::sleep_for(std::chrono::milliseconds(100));
				QApplication::processEvents();
				//lastTx = point.tx; lastTy = point.ty; lastTz = point.tz;
				continue;
			}
*/


			for (auto& item : items)
			{
				
				if (!item.transform.isMissing())
				{

					auto point = item.transform;
					
					//Local coordinate system
					Eigen::Quaternionf quatReference;
					Eigen::Vector3f ReferenceCoord;
					if (point.toolHandle == 11)
					{
						//Eigen::Quaternionf quatReference(point.q0, point.qx, point.qy, point.qz);
						//Eigen::Vector3f ReferenceCoord(point.tx, point.ty, point.tz);
						ReferenceCoord.x() = point.tx;
						ReferenceCoord.y() = point.ty;
						ReferenceCoord.z() = point.tz;
						quatReference.w()  = point.q0;
						quatReference.x()  = point.qx;
						quatReference.y()  = point.qy;
						quatReference.z()  = point.qz;					
						
						continue;
					}

					if (point.toolHandle == 10)
					{
						
						Eigen::Vector3f coord(point.tx, point.ty, point.tz);//
						//Eigen::Quaternionf quat(point.qz, point.q0, point.qx, point.qy);
						Eigen::Quaternionf quat(point.q0, point.qx, point.qy, point.qz);

						this->mRawScan.push_back(std::tuple<Eigen::Vector3f, Eigen::Quaternionf, int>(
							coord, quat, this->mUI->mResolutionComboBox->currentIndex()));


						//Local coordinate system Quaternion
						//Eigen::Quaternionf LocalQuaternion = quatReference.inverse() * quat;//.conjugate();
						//Eigen::Vector3f    LocalCoord = coord - ReferenceCoord;
						//quat = LocalQuaternion;
						//coord = LocalCoord;

						FAMPoint pt;
						pt.catheterCoord = coord;
						pt.catheterQuaternion = quat;
						pt.LAT = 0;
						pt.timestamp = ndiTimestamp + timestampBias;
						
						if ((abs(lastTx - point.tx) > DELTA) && (abs(lastTy - point.ty) > DELTA)
							&& (abs(lastTz - point.tz) > DELTA))
						{
							if ((abs(point.tx) < VOLUME_MAX) && (abs(point.tz) < VOLUME_MAX))
							{
								lastTx = point.tx; lastTy = point.ty; lastTz = point.tz;
								if (this->mReconstructor)
								{   //for pause function added by Yang 15:30 30/05 2024
									if (!mNDIFlag) {

										this->mReconstructor->onReceiveScanPoint(pt);
									}
									
								}
								else
								{
									std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);

									Eigen::AlignedBox3f bounds2;
									float halfScanSize = this->mUI->mScanSizeSpinBox->value() / 2.0f;
									tempp = halfScanSize;
									Eigen::Vector3f halfSize(halfScanSize, halfScanSize, halfScanSize);
									bounds2.extend(coord + halfSize);
									bounds2.extend(coord - halfSize);				
									halfSizepp= halfSize;
									FAMReconstructor* reconstructor = new FAMReconstructor(this->mCatheter, mPredefinedResolutions, this->mRespSamplingRate, bounds2);
									reconstructor->changeResolution(this->mUI->mResolutionComboBox->currentIndex(), true);
									reconstructor->setRespThreshold(this->mRespThreshold);
									this->mReconstructor.reset(reconstructor);

									// 设置可视化管理器的Volume
									if (mCatheterVisualizer) {
										mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
										mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
									}

									// 设置可视化管理器的Volume
									if (mCatheterVisualizer) {
										mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
										mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
									}

									// 设置可视化管理器的Volume
									if (mCatheterVisualizer) {
										mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
										mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
									}
									bounds2.setEmpty();
									halfSize /= 2;
									bounds2.extend(coord + halfSize);
									bounds2.extend(coord - halfSize);
									this->sceneWidget()->zoomBox(bounds2);
								}
							}
						}
						Eigen::Matrix4f ematrix = pt.transform().matrix();
						for (int m = 0; m < 4; m++)
							for (int n = 0; n < 4; n++)
								userTransformMatrix->SetElement(m, n, ematrix(m, n));

						this->mCatheterActor->SetUserMatrix(userTransformMatrix);
						this->mCatheterActor->ComputeMatrix();

						std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mLocalMeshLastUpdateTime;
						this->mCatheterUpdateFPS = 1000 / elapsed.count();
						this->mCatheterLastUpdateTime = std::chrono::steady_clock::now();						
					}

					this->updateScene();
					this->sceneWidget()->update();
					std::ostringstream output;
					output << setprecision(1) << ndiTimestamp << " , \t"
						<< localTimestamp - ndiTimestamp - timestampBias << ", \t"
						<< setprecision(0) << item.frameNumber << " , \t"
						<< point.toolHandle << ",  \t"
						<< point.status << " , \t"
						<< setprecision(2) << point.q0 << " , \t"
						<< setprecision(2) << point.qx << " , \t"
						<< setprecision(2) << point.qy << " , \t"
						<< setprecision(2) << point.qz << ",  \t"
						<< setprecision(1) << point.tx << ",  \t"
						<< setprecision(1) << point.ty << ",  \t" 
						<< setprecision(1) << point.tz
						<< ", Catheter Updated FPS: " << setprecision(1) << this->mCatheterUpdateFPS
						<< ", \t Local Mesh Updated FPS: " << setprecision(1) << this->mLocalMeshUpdateFPS
						<< ", \t Global Mesh Updated FPS: " << setprecision(1) << this->mGlobalMeshUpdateFPS
						<< std::endl;

					this->mUI->mStatusbar->showMessage(QString(output.str().c_str()));
					QApplication::processEvents();
				}
			}
		}
	}

	disconnect(mUI->mStartSimulatingBtn, &QPushButton::clicked, this, &MainWindow::endScan);
	connect(mUI->mStartSimulatingBtn, &QPushButton::clicked, this, &MainWindow::startScan);
	this->mUI->mStartSimulatingBtn->setText("Start Scan");
	QApplication::processEvents();
}

void MainWindow::closeEvent(QCloseEvent* ev)
{
	this->clear();
}

void MainWindow::simulateScanWithData()
{
	float halfScanSize = this->mUI->mScanSizeSpinBox->value() / 8.0f;
	tempp = halfScanSize;
	vtkSmartPointer<vtkMatrix4x4> userTransformMatrix = vtkSmartPointer<vtkMatrix4x4>::New();
	this->mUI->mStartSimulatingBtn->setEnabled(false);
	Eigen::AlignedBox3f bounds2;
	bounds2.extend(this->mBounds.min() * 2 - this->mBounds.center());
	bounds2.extend(this->mBounds.max() * 2 - this->mBounds.center());
	this->sceneWidget()->zoomBox(bounds2);
	this->mReconstructor.reset(new FAMReconstructor(this->mCatheter, mPredefinedResolutions, this->mRespSamplingRate, bounds2));
	this->mReconstructor->changeResolution(this->mUI->mResolutionComboBox->currentIndex(), true);
	this->mReconstructor->setRespThreshold(this->mRespThreshold);

	// 设置可视化管理器的Volume
	if (mCatheterVisualizer) {
		mCatheterVisualizer->setVolume(this->mReconstructor->currentVolume());
		mCatheterVisualizer->startVisualization(50);  // 50ms更新间隔
	}

	this->mCatheterLastUpdateTime = std::chrono::steady_clock::now();
	for (size_t i = 0; i < this->mRawScan.size(); i++)
	{
		std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mStartTimeStamp;
		double timestamp = elapsed.count();
		int sleepDuration = int(i * 20 - timestamp - 1000);
		if (sleepDuration > 0)
			std::this_thread::sleep_for(std::chrono::milliseconds(sleepDuration));
		
		const std::tuple<Eigen::Vector3f, Eigen::Quaternionf, int>& tuple = this->mRawScan[i];
		FAMPoint pt;
		pt.catheterCoord = std::get<0>(tuple);
		pt.catheterQuaternion = std::get<1>(tuple);
		pt.LAT = 0.0;
		pt.timestamp = timestamp;
		int spacingId = std::get<2>(tuple);
		if (spacingId >= 0)
		{
			if (spacingId != this->mUI->mResolutionComboBox->currentIndex())
			{
				//this->mUI->mResolutionComboBox->setCurrentIndex(spacingId);
			}
		}
		else
		{
			this->mRawScan[i] = std::tuple<Eigen::Vector3f, Eigen::Quaternionf, int>(
				pt.catheterCoord, pt.catheterQuaternion,
				this->mUI->mResolutionComboBox->currentIndex()
				);
		}
		this->mReconstructor->onReceiveScanPoint(pt);
		Eigen::Matrix4f ematrix = pt.transform().matrix();
		for (int m = 0; m < 4; m++)
			for (int n = 0; n < 4; n++)
				userTransformMatrix->SetElement(m, n, ematrix(m, n));

		this->mCatheterActor->SetUserMatrix(userTransformMatrix);
		this->mCatheterActor->ComputeMatrix();

		elapsed = std::chrono::steady_clock::now() - this->mLocalMeshLastUpdateTime;
		this->mCatheterUpdateFPS = 1000 / elapsed.count();
		this->mCatheterLastUpdateTime = std::chrono::steady_clock::now();

		this->updateScene();
		this->sceneWidget()->update();
		QApplication::processEvents();


		//const auto& respData = this->mReconstructor->respiration().data();
		//size_t respDataSize = respData.size();
		//for (int r = currentRespCount; r < respDataSize; r++)
		//{
		//	int count = respirationSeries->count();
		//	if (count > this->mRespSamplingRate * 60)
		//	{
		//		respirationSeries->removePoints(0, this->mRespSamplingRate * 10);
		//		this->mUI->mRespView->chart()->axisX()->setRange(r - this->mRespSamplingRate * 50,
		//			r + this->mRespSamplingRate * 10);
		//	}
		//	else
		//	{
		//		respirationSeries->append(count, respData[r].resp);
		//	}
		//}

		//currentRespCount = respDataSize;

		if (i % 50 == 0)
		{
			this->mUI->mStatusbar->showMessage(
				QString("Simulating Scan %1%, Catheter Update FPS: %2, Local Mesh Update FPS: %3, Global Mesh Update FPS: %4")
				.arg(i * 100 / this->mRawScan.size())
				.arg(this->mCatheterUpdateFPS)
				.arg(this->mLocalMeshUpdateFPS)
				.arg(this->mGlobalMeshUpdateFPS));
		}

		QApplication::processEvents();
	}

	this->mUI->mStartSimulatingBtn->setEnabled(true);
}

void MainWindow::endScan()
{
	this->mFinishing = true;
	if (this->mNDIDriver)
	{
		this->mNDIDriver->set_tracking_status(false);
		delete this->mNDIDriver;
		this->mNDIDriver = nullptr;
	}
}

void MainWindow::updateScene()
{
	if (this->mReconstructor)
	{
		// 导管点云可视化现在由CatheterVisualizer自动处理
		// 不需要在这里手动更新点云可视化
		
		if (this->mShowLocalMesh)
		{
			vtkSmartPointer<vtkPolyData> localMesh = this->mReconstructor->localMesh();
			if (localMesh && localMesh->GetNumberOfPoints() > 0)
			{
				if (this->mLocalActor)
				{
					if (this->sceneWidget()->updateDataSet(localMesh, "local"))
					{
						this->mLocalMesh = localMesh;
						std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mLocalMeshLastUpdateTime;
						this->mLocalMeshUpdateFPS = 1000 / elapsed.count();
						this->mLocalMeshLastUpdateTime = std::chrono::steady_clock::now();
					}
				}
				else
				{
					this->mLocalActor = this->sceneWidget()->addDataSet(localMesh,
						"local", Eigen::Vector4d(0.0, 0.0, 1.0, 1.0));
					// this->mLocalActor = this->sceneWidget()->addVolume(this->mLocalImage, "local");
					this->mLocalMesh = localMesh;
					this->mLocalMeshLastUpdateTime = std::chrono::steady_clock::now();
				}
			}
		}
		else
		{
			if (this->mLocalActor)
			{
				this->sceneWidget()->removeDataSet("local");
				this->mLocalActor = nullptr;
			}
		}

		if (this->mShowTransitionMesh)
		{
			vtkSmartPointer<vtkPolyData> transitionMesh = this->mReconstructor->transitionMesh();
			if (transitionMesh && transitionMesh->GetNumberOfPoints() > 0)
			{
				if (this->mTransitionActor)
				{
					if (this->sceneWidget()->updateDataSet(transitionMesh, "transition"))
					{
						this->mTransitionMesh = transitionMesh;
					}
				}
				else
				{
					this->mTransitionActor = this->sceneWidget()->addDataSet(transitionMesh,
						"transition", Eigen::Vector4d(1.0, 1.0, 0.0, 1.0));
					this->mTransitionMesh = transitionMesh;
				}
			}		
		}
		else
		{
			if (this->mTransitionActor)
			{
				this->sceneWidget()->removeDataSet("transition");
				this->mTransitionActor = nullptr;
			}
		}

		vtkSmartPointer<vtkPolyData> globalMesh = this->mReconstructor->globalMesh();
		// this->mGlobalImage = this->mReconstructor->currentVolume()->globalImage();
		if (globalMesh && globalMesh->GetNumberOfPoints() > 0)
		{
			if (this->mGlobalActor)
			{
				assert(globalMesh);
				if (this->sceneWidget()->updateDataSet(globalMesh, "global", this->mGlobalMeshOpacity))
				{
					this->mGlobalMesh = globalMesh;
					std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mGlobalMeshLastUpdateTime;
					this->mGlobalMeshUpdateFPS = 1000 / elapsed.count();
					this->mGlobalMeshLastUpdateTime = std::chrono::steady_clock::now();
				}
			}
			else
			{
				this->mGlobalActor = this->sceneWidget()->addDataSet(globalMesh,
					"global", Eigen::Vector4d(0.7, 0.7, 0.7, this->mGlobalMeshOpacity));
				//this->mGlobalActor = this->sceneWidget()->addDataSet(globalMesh, "global", "RESP", "rainbow");
				// this->mGlobalActor = this->sceneWidget()->addVolume(this->mGlobalImage, "global");
				this->mGlobalMesh = globalMesh;
				this->mGlobalMeshLastUpdateTime = std::chrono::steady_clock::now();
			}
		}
	}
	else
	{
		if (this->mShowLocalMesh)
		{
			if (this->mLocalMesh)
			{
				if (this->mLocalActor)
				{
					this->sceneWidget()->updateDataSet(this->mLocalMesh, "transition");
				}
				else
				{
					this->mLocalActor = this->sceneWidget()->addDataSet(this->mLocalMesh,
						"local", Eigen::Vector4d(0.0, 0.0, 1.0, 1.0));
				}
			}
		}
		else
		{
			if (this->mLocalActor)
			{
				this->sceneWidget()->removeDataSet("local");
				this->mLocalActor = nullptr;
			}
		}

		if (this->mShowTransitionMesh)
		{
			if (this->mTransitionMesh)
			{
				if (this->mTransitionActor)
				{
					this->sceneWidget()->updateDataSet(this->mTransitionMesh, "transition");
				}
				else
				{
					this->mTransitionActor = this->sceneWidget()->addDataSet(this->mTransitionMesh,
						"transition", Eigen::Vector4d(1.0, 1.0, 0.0, 1.0));
				}
			}
		}
		else
		{
			if (this->mTransitionActor)
			{
				this->sceneWidget()->removeDataSet("transition");
				this->mTransitionActor = nullptr;
			}
		}

		if (this->mGlobalMesh)
		{
			this->sceneWidget()->updateDataSet(this->mGlobalMesh, "global", this->mGlobalMeshOpacity);
		}
	}
}

void MainWindow::save()
{
	if (this->mFinished)
	{
		QString meshFilename = QFileDialog::getSaveFileName(this,
			"Save Mesh", "./mesh", "PLY (*.ply)");

		if (!meshFilename.isEmpty())
		{
			vtkSmartPointer<vtkPLYWriter> writer = vtkSmartPointer<vtkPLYWriter>::New();
			std::string str = meshFilename.toStdString();
			writer->SetFileName(str.c_str());
			writer->SetInputData(this->mGlobalMesh);
			writer->Write();
			writer->Update();
		}
/*
		QString volumeFilename = QFileDialog::getSaveFileName(this,
			"Save Volume", "./image", "Nifti Format (*.nii)");

		if (!volumeFilename.isEmpty())
		{
			vtkSmartPointer<vtkNIFTIImageWriter> writer = vtkSmartPointer<vtkNIFTIImageWriter>::New();
			std::string str = volumeFilename.toStdString();
			writer->SetFileName(str.c_str());
			writer->SetInputData(this->mReconstructor->baseVolume()->image());
			writer->Write();
			writer->Update();
		}
*/
		QString scanFilename = QFileDialog::getSaveFileName(this,
			"Save Scan Data", "./scandata", "TXT (*.txt)");

		if (!scanFilename.isEmpty())
		{
			QFile scanFile(scanFilename);
			if (scanFile.open(QIODevice::WriteOnly))
			{
				QTextStream textStream(&scanFile);
				float buffer[7];
				for (size_t i = 0; i < this->mRawScan.size(); ++i)
				{
					const auto& coord = std::get<0>(this->mRawScan[i]);
					const auto& quat = std::get<1>(this->mRawScan[i]);
					int spacingId = std::get<2>(this->mRawScan[i]);
					textStream << quat.w() << " " << quat.x() << " " << quat.y() << " " << quat.z()
						<< " " << coord.x() << " " << coord.y() << " " << coord.z() <<
                        " " << spacingId << Qt::endl;
				}

				textStream.flush();
				scanFile.close();
			}
		}
	}
}

void MainWindow::resolutionChanged(int currentIdx)
{
	if (this->mReconstructor)
	{
		if (this->mFinished)
		{
			this->mReconstructor->changeResolution(this->mUI->mResolutionComboBox->currentIndex(), true);
			this->updateScene();
		}
		else
		{
			this->mReconstructor->changeResolution(this->mUI->mResolutionComboBox->currentIndex(), false);
		}
	}
}

void MainWindow::respThresholdChanged(int value)
{	
	this->mRespThreshold = value / 10.0f;
	this->mUI->mRespirationThresholdLabel->setText(QString("Respiration Threshold: %1").arg(this->mRespThreshold));
	if (this->mReconstructor)
	{
		this->mReconstructor->setRespThreshold(this->mRespThreshold);
	}
}

void MainWindow::globalMeshOpacityChanged(int value)
{
	this->mGlobalMeshOpacity = value / 10.0;
	this->updateScene();
}

void MainWindow::toggleVisibility(int state)
{
	this->mShowLocalMesh = this->mUI->mLocalMeshCheckBox->isChecked();
	this->mShowTransitionMesh = this->mUI->mTransitionMeshCheckBox->isChecked();
	this->updateScene();
}

void MainWindow::addRespSignal(double timestamp, float resp)
{
	this->mUI->mRespView->graph(0)->addData(timestamp, resp);
	this->mUI->mRespView->graph(1)->addData(timestamp, this->mRespThreshold);
	if (timestamp - this->mRespLastUpdateTimestamp > 500)
	{
		this->mUI->mRespView->xAxis->setRange(timestamp, 30 * 1000.0, Qt::AlignRight);
		this->mUI->mRespView->replot();
		this->mRespLastUpdateTimestamp = timestamp;
	}
}

SceneWidget* MainWindow::sceneWidget()
{
	return mUI->mSceneWidget;
}

void MainWindow::emitRespiration()
{
	pthread_setname_np(pthread_self(), "emit_resp");
	bool toBreak = false;
	RespEmitter emitter;
	this->connect(&emitter, &RespEmitter::newRespSignal, this, &MainWindow::addRespSignal);
	
	// simulate a respiration signal with sin
	std::mt19937_64 rng;
	rng.seed(std::random_device()());	
	std::uniform_real_distribution<float> distribution(0.8f, 1.2f);
	const float PI = 3.1415926f;
	size_t i = 0;

	while(true)
	{
		std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
		if (this->mReconstructor)
			break;
	}

	this->mRespLastUpdateTimestamp = 0;
	while (!toBreak)
	{
		const std::chrono::duration<double, std::milli> elapsed = std::chrono::steady_clock::now() - this->mStartTimeStamp;
		double timestamp = elapsed.count();
		int sleepDuration = int(i * 1000 / this->mRespSamplingRate - timestamp);
		if (sleepDuration > 0)
			std::this_thread::sleep_for(std::chrono::milliseconds(sleepDuration));
		
		float resp;
		if (this->mRawRespiration.size() > 0)
		{
			resp = this->mRawRespiration[i];
		}
		else
		{
			float respPeriod = 1000 * 3.0f;// *distribution(rng);
			float amplitude = 5.0f * distribution(rng);
            resp = amplitude * std::sin((float)(timestamp * 2 * PI / respPeriod));
		}

		this->mReconstructor->onReceiveRespiration(resp, timestamp);
		emit emitter.newRespSignal(timestamp, resp);
		i++;		

		std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
		if (this->mStopEmitRespiration) toBreak = true; //make sure respiration thread is finished after scan thread
	}

	std::lock_guard<std::recursive_timed_mutex> lock(this->mMutex);
	this->mFinished = true;
}



