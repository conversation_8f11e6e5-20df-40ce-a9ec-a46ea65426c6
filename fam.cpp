#include "mainwindow.h"
#include <QApplication>
#include <QSurfaceFormat>
#include <QVTKOpenGLNativeWidget.h>
#include <vtkOutputWindow.h>
#include <QDebug>
#include <vtkDebugLeaks.h>

//#include <vtkDebug.h>
//#include "vtkQtDebugLeaksModel.h"
//#include "vtkQtDebugLeaksView.h"

int main(int argc, char* argv[])
{
    QSurfaceFormat::setDefaultFormat(QVTKOpenGLNativeWidget::defaultFormat());
    QApplication a(argc, argv);
    qDebug()<<"hello";
    vtkOutputWindow::SetGlobalWarningDisplay(0);
    MainWindow w;
    w.showMaximized();
    return a.exec();
}
