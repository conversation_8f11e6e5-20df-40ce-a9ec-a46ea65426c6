#include "reconstructor.h"
#include <chrono>
#include <thread>
#include <tbb/tbb.h>
#include <vtkCleanPolyData.h>
#include <vtkWindowedSincPolyDataFilter.h>
#include <vtkPointData.h>
#include <vtkFloatArray.h>
#include <vtkSmoothPolyDataFilter.h>
#include <vtkDecimatePro.h>
#include <vtkPolyDataNormals.h>
#include <vtkFillHolesFilter.h>
#include <vtkDistancePolyDataFilter.h>
#include <vtkAdaptiveSubdivisionFilter.h>
#include <vtkBooleanOperationPolyDataFilter.h>
#include <vtkImageResize.h>

unsigned int tempGlobal,tempLocal;
FAMReconstructor::FAMReconstructor(Catheter::Ptr catheter, const std::vector<float>& resolutions, int respSamplingRate, const Eigen::AlignedBox3f& bounds)
    : mCatheter(catheter)
    , mRespiration(respSamplingRate)
    , mBounds(bounds)
    , mLastMergedScanPointIndex(-1)
    , mCurrentResolutionId(0)
    , mAvailableResolutions(resolutions)
    , mLocalWorker(nullptr)
    , mGlobalWorker(nullptr)
    , mChangeResolutionWorker(nullptr)
    , mIsFinished(false)
{
    float minSpacing = this->mAvailableResolutions[0];//2
    this->mBaseVolume = FAMVolume::Ptr(new FAMVolume(minSpacing, this->mCatheter, bounds));

    this->mLocalWorker.reset(new std::thread(&FAMReconstructor::updateLocalMesh, this));
    this->mLocalWorker->detach();

    this->mGlobalWorker.reset(new std::thread(&FAMReconstructor::updateGlobalMesh, this, true));
    this->mGlobalWorker->detach();
}

FAMReconstructor::~FAMReconstructor()
{
    this->completeReconstruction();
    this->mCatheter = nullptr;
    this->mBaseVolume = nullptr;
    this->mCurrentVolume = nullptr;
    this->mLocalWorker = nullptr;
    this->mGlobalWorker = nullptr;
    this->mLocalMesh = nullptr;
    this->mGlobalMesh = nullptr;
    this->mChangeResolutionWorker = nullptr;
}

void FAMReconstructor::updateCurrentVolume()
{	
	if (this->mRespiration.data().size() < this->mRespiration.minNumToUpdate())
		return;

    if (this->mScanMutex.try_lock())
    {
        size_t scanDataSize = this->mRawScanData.size();

        if (this->mLastMergedScanPointIndex < (int)scanDataSize - 1)
        {
            if (this->checkRespirationThreshold(this->mRawScanData[scanDataSize - 1].timestamp))
            {
                this->mLocalCondition.notify_one();
            }
        }
        this->mScanMutex.unlock();
    }
}

void FAMReconstructor::setRespThreshold(float value)
{
    std::lock_guard<std::recursive_timed_mutex> scanLock(this->mScanMutex);
    this->mRespThreshold = value;
}

bool FAMReconstructor::checkRespirationThreshold(double timestamp)
{
    if (this->mRespiration.data().empty())
        return false;

    const Respiration::Sample& resp = this->mRespiration.lookup(timestamp);
    return resp.resp < this->mRespThreshold;
}

void FAMReconstructor::updateLocalMesh()
{
    pthread_setname_np(pthread_self(), "local");
    while (true)
    {        
        std::unique_lock<std::mutex> localWorkerLock(this->mLocalWorkerMutex);
        this->mLocalCondition.wait(localWorkerLock);

        if (this->isFinished())
            break;

        std::chrono::steady_clock::time_point startTimeStamp = std::chrono::steady_clock::now();

        std::vector<std::tuple<FAMPoint, int>> validPoints;
        {
            std::lock_guard<std::recursive_timed_mutex> scanLock(this->mScanMutex);
            int scanDataSize = this->mRawScanData.size();
            for (int i = this->mLastMergedScanPointIndex + 1; i < scanDataSize; i++)
            {
                if (this->checkRespirationThreshold(this->mRawScanData[i].timestamp))
                {
                    validPoints.push_back(std::tuple<FAMPoint, int>(this->mRawScanData[i], i));
                    this->mLastMergedScanPointIndex = i;
                }
            }
        }

        if (validPoints.size() == 0)
            continue;

        auto curVol = this->currentVolume();
        int temp = 0;
        for (int i = 0; i < validPoints.size(); i++)
        {
            temp = std::get<1>(validPoints[i]);
            curVol->addScanPoint2LocalVolume(std::get<0>(validPoints[i]), std::get<1>(validPoints[i]));
        }

        vtkSmartPointer<vtkPolyData> localMesh = this->currentVolume()->reconstruct();
        double bounds[6];
        localMesh->GetBounds(bounds);
        Eigen::AlignedBox3d ebounds(Eigen::Vector3d(bounds[0], bounds[2], bounds[4]),
            Eigen::Vector3d(bounds[1], bounds[3], bounds[5]));
        double maxSize = ebounds.sizes().maxCoeff();
        //check invalid polydata
        if (localMesh->GetNumberOfPoints() > 0 && maxSize > 1000)
            continue;

        {
            std::lock_guard<std::recursive_mutex> localLock(this->mLocalMutex);
            this->mLocalMesh = localMesh;
        }
        tempLocal= localMesh->GetNumberOfPoints();
        if (this->mGlobalWorkerMutex.try_lock())
        {
            std::lock_guard<std::recursive_mutex> transLock(this->mTransitionMutex);
            this->mTransitionMesh = this->mLocalMesh;
            this->mGlobalWorkerMutex.unlock();
            this->mGlobalCondition.notify_one();
            {
                std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
                this->mCurrentVolume->clear();                
              //  this->mCurrentVolume->setCenter(std::get<0>(validPoints.back()).catheterCoord);// 31/03 2025
            }
        }
    }
}

void FAMReconstructor::updateGlobalMesh(bool newThread)
{
    if (newThread)
    {
        pthread_setname_np(pthread_self(), "global");
    }

    while (true)
    {
        std::unique_lock<std::recursive_timed_mutex> globalWorkerLock(this->mGlobalWorkerMutex);
        this->mGlobalCondition.wait(globalWorkerLock);

        if (this->isFinished())
            break;

        std::chrono::steady_clock::time_point startTimeStamp = std::chrono::steady_clock::now();

        this->mBaseVolume->mergeMesh(this->transitionMesh());

        vtkSmartPointer<vtkPolyData> globalMesh = this->mBaseVolume->reconstruct();
        tempGlobal = globalMesh->GetNumberOfPoints();
        if (globalMesh->GetNumberOfPoints() > 0)
        {
            std::lock_guard<std::recursive_mutex> globalLock(this->mGlobalMutex);
            this->mGlobalMesh = globalMesh;
        }
    }
}

void FAMReconstructor::changeResolutionImp(int newResolutionId, bool join)
{
    std::lock_guard<std::recursive_mutex> workerLock(mChangeResolutionWorkerMutex);
    Eigen::AlignedBox3f quatBounds;
   // quatBounds.extend(this->mBounds.center() - this->mBounds.sizes() / 4);
   // quatBounds.extend(this->mBounds.center() + this->mBounds.sizes() / 4);
    quatBounds.extend(this->mBounds.center() - this->mBounds.sizes() / 2);
    quatBounds.extend(this->mBounds.center() + this->mBounds.sizes() / 2);
    FAMVolume::Ptr newVolume;
    pthread_setname_np(pthread_self(), "change resolution");                                                                                                                                                                                             
    {
        std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
        newVolume = FAMVolume::Ptr(
                new FAMVolume(this->mAvailableResolutions[newResolutionId], this->mCatheter, quatBounds));
    }

    {
        std::lock_guard<std::recursive_mutex> lock(mResolutionMutex);
        this->mCurrentResolutionId = newResolutionId;
        this->mCurrentVolume = newVolume;
    }
}

void FAMReconstructor::changeResolution(int newResolutionId, bool join)
{
    assert(newResolutionId < mAvailableResolutions.size());
    if (newResolutionId == this->mCurrentResolutionId && this->currentVolume())
        return;

    {
        if (this->mChangeResolutionWorker)
        {
            if (this->mChangeResolutionWorker->joinable())
                this->mChangeResolutionWorker->join();
            this->mChangeResolutionWorker = nullptr;
        }
    }

    this->mChangeResolutionWorker.reset(new std::thread(&FAMReconstructor::changeResolutionImp, this, newResolutionId, join));
    if (join)
    {
        this->mChangeResolutionWorker->join();
    }
    else
    {
        this->mChangeResolutionWorker->detach();
    }
}

void FAMReconstructor::onReceiveScanPoint(const FAMPoint& fpt)
{
    std::lock_guard<std::recursive_timed_mutex> scanLock(this->mScanMutex);
    assert(this->mRawScanData.size() == 0 || fpt.timestamp >= this->mRawScanData.back().timestamp);
    this->mRawScanData.push_back(fpt);
}

void FAMReconstructor::onReceiveRespiration(float resp, double timestamp)
{
    this->mRespirationCache.push_back(std::tuple<float, double>(resp, timestamp));
    if (this->mRespMutex.try_lock())
    {
        for (size_t i = 0; i < this->mRespirationCache.size(); ++i)
        {
            this->mRespiration.addSample(std::get<0>(this->mRespirationCache[i]), 
                std::get<1>(this->mRespirationCache[i]));
        }
        this->updateCurrentVolume();
        this->mRespMutex.unlock();
        this->mRespirationCache.clear();
    }
}

vtkSmartPointer<vtkPolyData> FAMReconstructor::localMesh()
{
    std::lock_guard<std::recursive_mutex> localLock(this->mLocalMutex);
    return this->mLocalMesh;
}

vtkSmartPointer<vtkPolyData> FAMReconstructor::globalMesh()
{
    std::lock_guard<std::recursive_mutex> localLock(this->mGlobalMutex);
    return this->mGlobalMesh;
}

vtkSmartPointer<vtkPolyData> FAMReconstructor::transitionMesh()
{
    std::lock_guard<std::recursive_mutex> transLock(this->mTransitionMutex);
    return this->mTransitionMesh;
}

void FAMReconstructor::completeReconstruction()
{
    {
        std::lock_guard<std::recursive_mutex> lock(this->mFinishMutex);
        this->mIsFinished = true;
    }
    {
        if (this->mLocalWorker)
        {
			{
				std::lock_guard<std::mutex> localLock(this->mLocalWorkerMutex);
				this->mLocalCondition.notify_one();
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
			}
            if (this->mLocalWorker->joinable())
                this->mLocalWorker->join();
            this->mLocalWorker = nullptr;
        }
    }

    {
        if (this->mGlobalWorker)
        {
            {
                std::lock_guard<std::recursive_timed_mutex> globalLock(this->mGlobalWorkerMutex);
                this->mGlobalCondition.notify_one();
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            if (this->mGlobalWorker->joinable())
                this->mGlobalWorker->join();
            this->mGlobalWorker = nullptr;
        }
    }

    {        
        if (this->mChangeResolutionWorker)
        {
            std::lock_guard<std::recursive_mutex> changeResolutionLock(mChangeResolutionWorkerMutex);
            this->mChangeResolutionWorker = nullptr;
        }
    }

    {
        std::lock_guard<std::recursive_timed_mutex> resplock(this->mRespMutex);
        this->mRespiration.clear();
    }

    {
        std::lock_guard<std::recursive_timed_mutex> scanLock(this->mScanMutex);
        this->mRawScanData.clear();
    }

    //this->updateGlobalMesh(false);
}
