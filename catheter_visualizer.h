#ifndef CATHETER_VISUALIZER_H
#define CATHETER_VISUALIZER_H

#include <memory>
#include <vector>
#include <mutex>
#include <atomic>
#include <chrono>
#include <QObject>
#include <QTimer>

#ifndef EIGEN_USE_BLAS
#define EIGEN_USE_BLAS
#endif
#include <Eigen/Eigen>

#include <vtkSmartPointer.h>
#include <vtkPoints.h>
#include <vtkPolyData.h>
#include <vtkActor.h>

class SceneWidget;
class FAMVolume;

/**
 * @brief 导管点云可视化管理器
 * 
 * 负责管理导管点云的实时可视化，确保线程安全和性能优化
 * 主要功能：
 * 1. 线程安全的点云数据管理
 * 2. 实时可视化更新
 * 3. 内存优化和性能控制
 * 4. 多线程环境下的数据同步
 */
class CatheterVisualizer : public QObject
{
    Q_OBJECT

public:
    explicit CatheterVisualizer(SceneWidget* sceneWidget, QObject* parent = nullptr);
    ~CatheterVisualizer();

    /**
     * @brief 设置要可视化的FAMVolume对象
     * @param volume FAMVolume智能指针
     */
    void setVolume(std::shared_ptr<FAMVolume> volume);

    /**
     * @brief 启动实时可视化更新
     * @param updateIntervalMs 更新间隔（毫秒）
     */
    void startVisualization(int updateIntervalMs = 50);

    /**
     * @brief 停止可视化更新
     */
    void stopVisualization();

    /**
     * @brief 清除所有可视化数据
     */
    void clearVisualization();

    /**
     * @brief 设置点云显示属性
     * @param pointSize 点的大小
     * @param color 点的颜色 (R, G, B, A)
     * @param opacity 透明度
     */
    void setPointCloudProperties(float pointSize = 2.0f, 
                                const Eigen::Vector4f& color = Eigen::Vector4f(0.0f, 1.0f, 0.0f, 1.0f),
                                float opacity = 1.0f);

    /**
     * @brief 设置最大显示点数（用于性能控制）
     * @param maxPoints 最大点数，0表示无限制
     */
    void setMaxDisplayPoints(size_t maxPoints);

    /**
     * @brief 获取当前显示的点数
     */
    size_t getCurrentPointCount() const;

    /**
     * @brief 获取累积的总点数
     */
    size_t getTotalPointCount() const;

private slots:
    /**
     * @brief 定时器触发的更新函数
     */
    void updateVisualization();

private:
    /**
     * @brief 创建VTK点云数据
     * @param points 点坐标向量
     * @return VTK PolyData对象
     */
    vtkSmartPointer<vtkPolyData> createPointCloudPolyData(const std::vector<Eigen::Vector3f>& points);

    /**
     * @brief 线程安全地更新场景中的点云
     * @param polyData 新的点云数据
     */
    void updateScenePointCloud(vtkSmartPointer<vtkPolyData> polyData);

    /**
     * @brief 应用点云下采样（用于性能优化）
     * @param points 输入点云
     * @param maxPoints 最大点数
     * @return 下采样后的点云
     */
    std::vector<Eigen::Vector3f> downsamplePoints(const std::vector<Eigen::Vector3f>& points, 
                                                  size_t maxPoints);

private:
    SceneWidget* mSceneWidget;
    std::shared_ptr<FAMVolume> mVolume;
    
    // 可视化控制
    QTimer* mUpdateTimer;
    std::atomic<bool> mIsActive;
    
    // 点云属性
    float mPointSize;
    Eigen::Vector4f mPointColor;
    float mOpacity;
    size_t mMaxDisplayPoints;
    
    // 统计信息
    mutable std::mutex mStatsMutex;
    size_t mCurrentPointCount;
    size_t mTotalPointCount;
    
    // VTK对象管理
    vtkSmartPointer<vtkActor> mPointCloudActor;
    vtkSmartPointer<vtkPolyData> mCurrentPolyData;
    
    // 性能监控
    std::chrono::steady_clock::time_point mLastUpdateTime;
    std::chrono::duration<double, std::milli> mAverageUpdateTime;
    size_t mUpdateCount;
    
    static const std::string POINT_CLOUD_ACTOR_NAME;
};

#endif // CATHETER_VISUALIZER_H
