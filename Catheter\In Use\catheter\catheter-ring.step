ISO-10303-21;
HEADER;
FILE_DESCRIPTION(('FreeCAD Model'),'2;1');
FILE_NAME('Open CASCADE Shape Model','2023-06-12T11:27:19',('Author'),(
    ''),'Open CASCADE STEP processor 7.6','FreeCAD','Unknown');
FILE_SCHEMA(('AUTOMOTIVE_DESIGN { 1 0 10303 214 1 1 1 1 }'));
ENDSEC;
DATA;
#1 = APPLICATION_PROTOCOL_DEFINITION('international standard',
  'automotive_design',2000,#2);
#2 = APPLICATION_CONTEXT(
  'core data for automotive mechanical design processes');
#3 = SHAPE_DEFINITION_REPRESENTATION(#4,#10);
#4 = PRODUCT_DEFINITION_SHAPE('','',#5);
#5 = PRODUCT_DEFINITION('design','',#6,#9);
#6 = PRODUCT_DEFINITION_FORMATION('','',#7);
#7 = PRODUCT('catheter_ring','catheter_ring','',(#8));
#8 = PRODUCT_CONTEXT('',#2,'mechanical');
#9 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#10 = SHAPE_REPRESENTATION('',(#11,#15,#19,#23),#27);
#11 = AXIS2_PLACEMENT_3D('',#12,#13,#14);
#12 = CARTESIAN_POINT('',(0.,0.,0.));
#13 = DIRECTION('',(0.,0.,1.));
#14 = DIRECTION('',(1.,0.,-0.));
#15 = AXIS2_PLACEMENT_3D('',#16,#17,#18);
#16 = CARTESIAN_POINT('',(0.,0.,0.));
#17 = DIRECTION('',(0.,0.,1.));
#18 = DIRECTION('',(1.,0.,0.));
#19 = AXIS2_PLACEMENT_3D('',#20,#21,#22);
#20 = CARTESIAN_POINT('',(0.,0.,0.));
#21 = DIRECTION('',(0.,-0.,1.));
#22 = DIRECTION('',(0.809016994375,0.587785252292,0.));
#23 = AXIS2_PLACEMENT_3D('',#24,#25,#26);
#24 = CARTESIAN_POINT('',(0.,0.,16.));
#25 = DIRECTION('',(0.,0.,1.));
#26 = DIRECTION('',(1.,0.,0.));
#27 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#31)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#28,#29,#30)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#28 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#29 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#30 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#31 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#28,
  'distance_accuracy_value','confusion accuracy');
#32 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#7));
#33 = SHAPE_DEFINITION_REPRESENTATION(#34,#40);
#34 = PRODUCT_DEFINITION_SHAPE('','',#35);
#35 = PRODUCT_DEFINITION('design','',#36,#39);
#36 = PRODUCT_DEFINITION_FORMATION('','',#37);
#37 = PRODUCT('wiring','wiring','',(#38));
#38 = PRODUCT_CONTEXT('',#2,'mechanical');
#39 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#40 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#41),#57);
#41 = MANIFOLD_SOLID_BREP('',#42);
#42 = CLOSED_SHELL('',(#43));
#43 = ADVANCED_FACE('',(#44),#48,.F.);
#44 = FACE_BOUND('',#45,.F.);
#45 = VERTEX_LOOP('',#46);
#46 = VERTEX_POINT('',#47);
#47 = CARTESIAN_POINT('',(-3.E-15,-8.85,6.E-15));
#48 = SURFACE_OF_REVOLUTION('',#49,#54);
#49 = CIRCLE('',#50,0.65);
#50 = AXIS2_PLACEMENT_3D('',#51,#52,#53);
#51 = CARTESIAN_POINT('',(-2.11E-15,-9.5,5.27E-15));
#52 = DIRECTION('',(1.,-2.2E-16,5.6E-16));
#53 = DIRECTION('',(2.2E-16,1.,-5.6E-16));
#54 = AXIS1_PLACEMENT('',#55,#56);
#55 = CARTESIAN_POINT('',(0.,0.,0.));
#56 = DIRECTION('',(-5.6E-16,3.9E-16,1.));
#57 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#61)) GLOBAL_UNIT_ASSIGNED_CONTEXT(
(#58,#59,#60)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#58 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#59 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#60 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#61 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#58,
  'distance_accuracy_value','confusion accuracy');
#62 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#63,#65);
#63 = ( REPRESENTATION_RELATIONSHIP('','',#40,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#64) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#64 = ITEM_DEFINED_TRANSFORMATION('','',#11,#15);
#65 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#66);
#66 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('9','wiring','',#5,#35,$);
#67 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#37));
#68 = SHAPE_DEFINITION_REPRESENTATION(#69,#75);
#69 = PRODUCT_DEFINITION_SHAPE('','',#70);
#70 = PRODUCT_DEFINITION('design','',#71,#74);
#71 = PRODUCT_DEFINITION_FORMATION('','',#72);
#72 = PRODUCT('sensor','sensor','',(#73));
#73 = PRODUCT_CONTEXT('',#2,'mechanical');
#74 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#75 = SHAPE_REPRESENTATION('',(#11,#76,#80,#84,#88,#92,#96,#100,#104,
    #108),#112);
#76 = AXIS2_PLACEMENT_3D('',#77,#78,#79);
#77 = CARTESIAN_POINT('',(0.,0.,0.));
#78 = DIRECTION('',(0.,0.,1.));
#79 = DIRECTION('',(1.,0.,0.));
#80 = AXIS2_PLACEMENT_3D('',#81,#82,#83);
#81 = CARTESIAN_POINT('',(0.,0.,0.));
#82 = DIRECTION('',(0.,-0.,1.));
#83 = DIRECTION('',(0.831469612303,0.55557023302,0.));
#84 = AXIS2_PLACEMENT_3D('',#85,#86,#87);
#85 = CARTESIAN_POINT('',(0.,0.,0.));
#86 = DIRECTION('',(0.,-0.,1.));
#87 = DIRECTION('',(0.382683432365,0.923879532511,0.));
#88 = AXIS2_PLACEMENT_3D('',#89,#90,#91);
#89 = CARTESIAN_POINT('',(0.,0.,0.));
#90 = DIRECTION('',(0.,0.,1.));
#91 = DIRECTION('',(-0.195090322016,0.980785280403,0.));
#92 = AXIS2_PLACEMENT_3D('',#93,#94,#95);
#93 = CARTESIAN_POINT('',(0.,0.,0.));
#94 = DIRECTION('',(0.,0.,1.));
#95 = DIRECTION('',(-0.707106781187,0.707106781187,0.));
#96 = AXIS2_PLACEMENT_3D('',#97,#98,#99);
#97 = CARTESIAN_POINT('',(0.,0.,0.));
#98 = DIRECTION('',(0.,0.,1.));
#99 = DIRECTION('',(-0.980785280403,0.195090322016,0.));
#100 = AXIS2_PLACEMENT_3D('',#101,#102,#103);
#101 = CARTESIAN_POINT('',(0.,0.,0.));
#102 = DIRECTION('',(0.,0.,1.));
#103 = DIRECTION('',(-0.923879532511,-0.382683432365,0.));
#104 = AXIS2_PLACEMENT_3D('',#105,#106,#107);
#105 = CARTESIAN_POINT('',(0.,0.,0.));
#106 = DIRECTION('',(0.,0.,1.));
#107 = DIRECTION('',(-0.55557023302,-0.831469612303,0.));
#108 = AXIS2_PLACEMENT_3D('',#109,#110,#111);
#109 = CARTESIAN_POINT('',(0.,0.,0.));
#110 = DIRECTION('',(0.,0.,1.));
#111 = DIRECTION('',(-2.22044604925E-16,-1.,0.));
#112 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#116)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#113,#114,#115)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#113 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#114 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#115 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#116 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#113,
  'distance_accuracy_value','confusion accuracy');
#117 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#118,#120);
#118 = ( REPRESENTATION_RELATIONSHIP('','',#75,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#119) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#119 = ITEM_DEFINED_TRANSFORMATION('','',#11,#19);
#120 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#121
  );
#121 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('19','sensor','',#5,#70,$);
#122 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#72));
#123 = SHAPE_DEFINITION_REPRESENTATION(#124,#130);
#124 = PRODUCT_DEFINITION_SHAPE('','',#125);
#125 = PRODUCT_DEFINITION('design','',#126,#129);
#126 = PRODUCT_DEFINITION_FORMATION('','',#127);
#127 = PRODUCT('polar','polar','',(#128));
#128 = PRODUCT_CONTEXT('',#2,'mechanical');
#129 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#130 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#131),#234);
#131 = MANIFOLD_SOLID_BREP('',#132);
#132 = CLOSED_SHELL('',(#133,#226,#230));
#133 = ADVANCED_FACE('',(#134),#149,.F.);
#134 = FACE_BOUND('',#135,.F.);
#135 = EDGE_LOOP('',(#136,#171,#198,#199));
#136 = ORIENTED_EDGE('',*,*,#137,.T.);
#137 = EDGE_CURVE('',#138,#140,#142,.T.);
#138 = VERTEX_POINT('',#139);
#139 = CARTESIAN_POINT('',(-0.55562225616,-8.782441796474,6.E-15));
#140 = VERTEX_POINT('',#141);
#141 = CARTESIAN_POINT('',(0.55562225616,-8.782441796474,6.E-15));
#142 = SEAM_CURVE('',#143,(#148,#164),.PCURVE_S1.);
#143 = CIRCLE('',#144,8.8);
#144 = AXIS2_PLACEMENT_3D('',#145,#146,#147);
#145 = CARTESIAN_POINT('',(-0.,0.,1.58E-15));
#146 = DIRECTION('',(-5.6E-16,3.9E-16,1.));
#147 = DIRECTION('',(-6.31388927455E-02,-0.998004749599,
    3.538640724063E-16));
#148 = PCURVE('',#149,#158);
#149 = SURFACE_OF_REVOLUTION('',#150,#155);
#150 = CIRCLE('',#151,0.7);
#151 = AXIS2_PLACEMENT_3D('',#152,#153,#154);
#152 = CARTESIAN_POINT('',(-0.599819481082,-9.481045121194,5.06E-15));
#153 = DIRECTION('',(0.998004749599,-6.31388927455E-02,5.8E-16));
#154 = DIRECTION('',(6.31388927455E-02,0.998004749599,-5.3E-16));
#155 = AXIS1_PLACEMENT('',#156,#157);
#156 = CARTESIAN_POINT('',(0.,0.,0.));
#157 = DIRECTION('',(-5.6E-16,3.9E-16,1.));
#158 = DEFINITIONAL_REPRESENTATION('',(#159),#163);
#159 = LINE('',#160,#161);
#160 = CARTESIAN_POINT('',(0.,0.));
#161 = VECTOR('',#162,1.);
#162 = DIRECTION('',(1.,0.));
#163 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#164 = PCURVE('',#149,#165);
#165 = DEFINITIONAL_REPRESENTATION('',(#166),#170);
#166 = LINE('',#167,#168);
#167 = CARTESIAN_POINT('',(0.,6.28318530718));
#168 = VECTOR('',#169,1.);
#169 = DIRECTION('',(1.,0.));
#170 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#171 = ORIENTED_EDGE('',*,*,#172,.T.);
#172 = EDGE_CURVE('',#140,#140,#173,.T.);
#173 = SURFACE_CURVE('',#174,(#179,#186),.PCURVE_S1.);
#174 = CIRCLE('',#175,0.7);
#175 = AXIS2_PLACEMENT_3D('',#176,#177,#178);
#176 = CARTESIAN_POINT('',(0.599819481082,-9.481045121194,
    2.872702076218E-15));
#177 = DIRECTION('',(0.998004749599,6.31388927455E-02,
    -6.066302108989E-17));
#178 = DIRECTION('',(-6.31388927455E-02,0.998004749599,
    -2.99239799606E-16));
#179 = PCURVE('',#149,#180);
#180 = DEFINITIONAL_REPRESENTATION('',(#181),#185);
#181 = LINE('',#182,#183);
#182 = CARTESIAN_POINT('',(0.126361837844,0.));
#183 = VECTOR('',#184,1.);
#184 = DIRECTION('',(0.,1.));
#185 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#186 = PCURVE('',#187,#192);
#187 = PLANE('',#188);
#188 = AXIS2_PLACEMENT_3D('',#189,#190,#191);
#189 = CARTESIAN_POINT('',(0.599819481082,-9.481045121194,
    5.419276138952E-15));
#190 = DIRECTION('',(0.998004749599,6.31388927455E-02,
    -3.053113317719E-16));
#191 = DIRECTION('',(3.053113317719E-16,-5.898059818321E-17,1.));
#192 = DEFINITIONAL_REPRESENTATION('',(#193),#197);
#193 = CIRCLE('',#194,0.7);
#194 = AXIS2_PLACEMENT_2D('',#195,#196);
#195 = CARTESIAN_POINT('',(-2.548672387411E-15,1.783703435588E-15));
#196 = DIRECTION('',(-3.780520367008E-16,-1.));
#197 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#198 = ORIENTED_EDGE('',*,*,#137,.F.);
#199 = ORIENTED_EDGE('',*,*,#200,.F.);
#200 = EDGE_CURVE('',#138,#138,#201,.T.);
#201 = SURFACE_CURVE('',#202,(#207,#214),.PCURVE_S1.);
#202 = CIRCLE('',#203,0.7);
#203 = AXIS2_PLACEMENT_3D('',#204,#205,#206);
#204 = CARTESIAN_POINT('',(-0.599819481082,-9.481045121194,
    3.205768983605E-15));
#205 = DIRECTION('',(0.998004749599,-6.31388927455E-02,2.85276625714E-16
    ));
#206 = DIRECTION('',(6.31388927455E-02,0.998004749599,
    -3.330669073875E-16));
#207 = PCURVE('',#149,#208);
#208 = DEFINITIONAL_REPRESENTATION('',(#209),#213);
#209 = LINE('',#210,#211);
#210 = CARTESIAN_POINT('',(0.,0.));
#211 = VECTOR('',#212,1.);
#212 = DIRECTION('',(0.,1.));
#213 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#214 = PCURVE('',#215,#220);
#215 = PLANE('',#216);
#216 = AXIS2_PLACEMENT_3D('',#217,#218,#219);
#217 = CARTESIAN_POINT('',(-0.599819481082,-9.481045121194,
    5.20417042793E-15));
#218 = DIRECTION('',(0.998004749599,-6.31388927455E-02,
    1.387778780781E-16));
#219 = DIRECTION('',(-1.387778780781E-16,-1.49186218934E-16,1.));
#220 = DEFINITIONAL_REPRESENTATION('',(#221),#225);
#221 = CIRCLE('',#222,0.7);
#222 = AXIS2_PLACEMENT_2D('',#223,#224);
#223 = CARTESIAN_POINT('',(-2.008433783538E-15,1.778837573606E-15));
#224 = DIRECTION('',(-4.888943563624E-16,-1.));
#225 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#226 = ADVANCED_FACE('',(#227),#215,.F.);
#227 = FACE_BOUND('',#228,.F.);
#228 = EDGE_LOOP('',(#229));
#229 = ORIENTED_EDGE('',*,*,#200,.T.);
#230 = ADVANCED_FACE('',(#231),#187,.T.);
#231 = FACE_BOUND('',#232,.T.);
#232 = EDGE_LOOP('',(#233));
#233 = ORIENTED_EDGE('',*,*,#172,.T.);
#234 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#238)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#235,#236,#237)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#235 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#236 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#237 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#238 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#235,
  'distance_accuracy_value','confusion accuracy');
#239 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#240,#242);
#240 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#241) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#241 = ITEM_DEFINED_TRANSFORMATION('','',#11,#76);
#242 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#243
  );
#243 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('10','0','',#70,#125,$);
#244 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#127));
#245 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#246,#248);
#246 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#247) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#247 = ITEM_DEFINED_TRANSFORMATION('','',#11,#80);
#248 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#249
  );
#249 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('11','1','',#70,#125,$);
#250 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#251,#253);
#251 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#252) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#252 = ITEM_DEFINED_TRANSFORMATION('','',#11,#84);
#253 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#254
  );
#254 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('12','2','',#70,#125,$);
#255 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#256,#258);
#256 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#257) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#257 = ITEM_DEFINED_TRANSFORMATION('','',#11,#88);
#258 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#259
  );
#259 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('13','3','',#70,#125,$);
#260 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#261,#263);
#261 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#262) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#262 = ITEM_DEFINED_TRANSFORMATION('','',#11,#92);
#263 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#264
  );
#264 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('14','4','',#70,#125,$);
#265 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#266,#268);
#266 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#267) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#267 = ITEM_DEFINED_TRANSFORMATION('','',#11,#96);
#268 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#269
  );
#269 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('15','5','',#70,#125,$);
#270 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#271,#273);
#271 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#272) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#272 = ITEM_DEFINED_TRANSFORMATION('','',#11,#100);
#273 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#274
  );
#274 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('16','6','',#70,#125,$);
#275 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#276,#278);
#276 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#277) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#277 = ITEM_DEFINED_TRANSFORMATION('','',#11,#104);
#278 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#279
  );
#279 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('17','7','',#70,#125,$);
#280 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#281,#283);
#281 = ( REPRESENTATION_RELATIONSHIP('','',#130,#75) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#282) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#282 = ITEM_DEFINED_TRANSFORMATION('','',#11,#108);
#283 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#284
  );
#284 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('18','8','',#70,#125,$);
#285 = SHAPE_DEFINITION_REPRESENTATION(#286,#292);
#286 = PRODUCT_DEFINITION_SHAPE('','',#287);
#287 = PRODUCT_DEFINITION('design','',#288,#291);
#288 = PRODUCT_DEFINITION_FORMATION('','',#289);
#289 = PRODUCT('pipe','pipe','',(#290));
#290 = PRODUCT_CONTEXT('',#2,'mechanical');
#291 = PRODUCT_DEFINITION_CONTEXT('part definition',#2,'design');
#292 = ADVANCED_BREP_SHAPE_REPRESENTATION('',(#11,#293),#391);
#293 = MANIFOLD_SOLID_BREP('',#294);
#294 = CLOSED_SHELL('',(#295,#383,#387));
#295 = ADVANCED_FACE('',(#296),#310,.T.);
#296 = FACE_BOUND('',#297,.T.);
#297 = EDGE_LOOP('',(#298,#328,#355,#356));
#298 = ORIENTED_EDGE('',*,*,#299,.T.);
#299 = EDGE_CURVE('',#300,#302,#304,.T.);
#300 = VERTEX_POINT('',#301);
#301 = CARTESIAN_POINT('',(0.65,-9.5,0.));
#302 = VERTEX_POINT('',#303);
#303 = CARTESIAN_POINT('',(0.65,-9.5,-16.));
#304 = SEAM_CURVE('',#305,(#309,#321),.PCURVE_S1.);
#305 = LINE('',#306,#307);
#306 = CARTESIAN_POINT('',(0.65,-9.5,0.));
#307 = VECTOR('',#308,1.);
#308 = DIRECTION('',(-0.,-0.,-1.));
#309 = PCURVE('',#310,#315);
#310 = CYLINDRICAL_SURFACE('',#311,0.65);
#311 = AXIS2_PLACEMENT_3D('',#312,#313,#314);
#312 = CARTESIAN_POINT('',(0.,-9.5,0.));
#313 = DIRECTION('',(0.,0.,1.));
#314 = DIRECTION('',(1.,0.,-0.));
#315 = DEFINITIONAL_REPRESENTATION('',(#316),#320);
#316 = LINE('',#317,#318);
#317 = CARTESIAN_POINT('',(0.,0.));
#318 = VECTOR('',#319,1.);
#319 = DIRECTION('',(0.,-1.));
#320 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#321 = PCURVE('',#310,#322);
#322 = DEFINITIONAL_REPRESENTATION('',(#323),#327);
#323 = LINE('',#324,#325);
#324 = CARTESIAN_POINT('',(6.28318530718,0.));
#325 = VECTOR('',#326,1.);
#326 = DIRECTION('',(0.,-1.));
#327 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#328 = ORIENTED_EDGE('',*,*,#329,.T.);
#329 = EDGE_CURVE('',#302,#302,#330,.T.);
#330 = SURFACE_CURVE('',#331,(#336,#343),.PCURVE_S1.);
#331 = CIRCLE('',#332,0.65);
#332 = AXIS2_PLACEMENT_3D('',#333,#334,#335);
#333 = CARTESIAN_POINT('',(0.,-9.5,-16.));
#334 = DIRECTION('',(0.,0.,1.));
#335 = DIRECTION('',(1.,0.,0.));
#336 = PCURVE('',#310,#337);
#337 = DEFINITIONAL_REPRESENTATION('',(#338),#342);
#338 = LINE('',#339,#340);
#339 = CARTESIAN_POINT('',(0.,-16.));
#340 = VECTOR('',#341,1.);
#341 = DIRECTION('',(1.,0.));
#342 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#343 = PCURVE('',#344,#349);
#344 = PLANE('',#345);
#345 = AXIS2_PLACEMENT_3D('',#346,#347,#348);
#346 = CARTESIAN_POINT('',(-1.E-16,-9.5,-16.));
#347 = DIRECTION('',(0.,0.,1.));
#348 = DIRECTION('',(1.,0.,0.));
#349 = DEFINITIONAL_REPRESENTATION('',(#350),#354);
#350 = CIRCLE('',#351,0.65);
#351 = AXIS2_PLACEMENT_2D('',#352,#353);
#352 = CARTESIAN_POINT('',(1.E-16,-1.7763568394E-15));
#353 = DIRECTION('',(1.,0.));
#354 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#355 = ORIENTED_EDGE('',*,*,#299,.F.);
#356 = ORIENTED_EDGE('',*,*,#357,.F.);
#357 = EDGE_CURVE('',#300,#300,#358,.T.);
#358 = SURFACE_CURVE('',#359,(#364,#371),.PCURVE_S1.);
#359 = CIRCLE('',#360,0.65);
#360 = AXIS2_PLACEMENT_3D('',#361,#362,#363);
#361 = CARTESIAN_POINT('',(0.,-9.5,0.));
#362 = DIRECTION('',(0.,0.,1.));
#363 = DIRECTION('',(1.,0.,0.));
#364 = PCURVE('',#310,#365);
#365 = DEFINITIONAL_REPRESENTATION('',(#366),#370);
#366 = LINE('',#367,#368);
#367 = CARTESIAN_POINT('',(0.,0.));
#368 = VECTOR('',#369,1.);
#369 = DIRECTION('',(1.,0.));
#370 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#371 = PCURVE('',#372,#377);
#372 = PLANE('',#373);
#373 = AXIS2_PLACEMENT_3D('',#374,#375,#376);
#374 = CARTESIAN_POINT('',(-1.E-16,-9.5,0.));
#375 = DIRECTION('',(0.,0.,1.));
#376 = DIRECTION('',(1.,0.,0.));
#377 = DEFINITIONAL_REPRESENTATION('',(#378),#382);
#378 = CIRCLE('',#379,0.65);
#379 = AXIS2_PLACEMENT_2D('',#380,#381);
#380 = CARTESIAN_POINT('',(1.E-16,-1.7763568394E-15));
#381 = DIRECTION('',(1.,0.));
#382 = ( GEOMETRIC_REPRESENTATION_CONTEXT(2) 
PARAMETRIC_REPRESENTATION_CONTEXT() REPRESENTATION_CONTEXT('2D SPACE',''
  ) );
#383 = ADVANCED_FACE('',(#384),#372,.T.);
#384 = FACE_BOUND('',#385,.T.);
#385 = EDGE_LOOP('',(#386));
#386 = ORIENTED_EDGE('',*,*,#357,.T.);
#387 = ADVANCED_FACE('',(#388),#344,.F.);
#388 = FACE_BOUND('',#389,.F.);
#389 = EDGE_LOOP('',(#390));
#390 = ORIENTED_EDGE('',*,*,#329,.T.);
#391 = ( GEOMETRIC_REPRESENTATION_CONTEXT(3) 
GLOBAL_UNCERTAINTY_ASSIGNED_CONTEXT((#395)) GLOBAL_UNIT_ASSIGNED_CONTEXT
((#392,#393,#394)) REPRESENTATION_CONTEXT('Context #1',
  '3D Context with UNIT and UNCERTAINTY') );
#392 = ( LENGTH_UNIT() NAMED_UNIT(*) SI_UNIT(.MILLI.,.METRE.) );
#393 = ( NAMED_UNIT(*) PLANE_ANGLE_UNIT() SI_UNIT($,.RADIAN.) );
#394 = ( NAMED_UNIT(*) SI_UNIT($,.STERADIAN.) SOLID_ANGLE_UNIT() );
#395 = UNCERTAINTY_MEASURE_WITH_UNIT(LENGTH_MEASURE(1.E-07),#392,
  'distance_accuracy_value','confusion accuracy');
#396 = CONTEXT_DEPENDENT_SHAPE_REPRESENTATION(#397,#399);
#397 = ( REPRESENTATION_RELATIONSHIP('','',#292,#10) 
REPRESENTATION_RELATIONSHIP_WITH_TRANSFORMATION(#398) 
SHAPE_REPRESENTATION_RELATIONSHIP() );
#398 = ITEM_DEFINED_TRANSFORMATION('','',#11,#23);
#399 = PRODUCT_DEFINITION_SHAPE('Placement','Placement of an item',#400
  );
#400 = NEXT_ASSEMBLY_USAGE_OCCURRENCE('20','pipe','',#5,#287,$);
#401 = PRODUCT_RELATED_PRODUCT_CATEGORY('part',$,(#289));
#402 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#403)
  ,#57);
#403 = STYLED_ITEM('color',(#404),#41);
#404 = PRESENTATION_STYLE_ASSIGNMENT((#405,#411));
#405 = SURFACE_STYLE_USAGE(.BOTH.,#406);
#406 = SURFACE_SIDE_STYLE('',(#407));
#407 = SURFACE_STYLE_FILL_AREA(#408);
#408 = FILL_AREA_STYLE('',(#409));
#409 = FILL_AREA_STYLE_COLOUR('',#410);
#410 = COLOUR_RGB('',0.,0.666666687201,1.);
#411 = CURVE_STYLE('',#412,POSITIVE_LENGTH_MEASURE(0.1),#413);
#412 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#413 = COLOUR_RGB('',9.803921802644E-02,9.803921802644E-02,
  9.803921802644E-02);
#414 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#415)
  ,#234);
#415 = STYLED_ITEM('color',(#416),#131);
#416 = PRESENTATION_STYLE_ASSIGNMENT((#417,#423));
#417 = SURFACE_STYLE_USAGE(.BOTH.,#418);
#418 = SURFACE_SIDE_STYLE('',(#419));
#419 = SURFACE_STYLE_FILL_AREA(#420);
#420 = FILL_AREA_STYLE('',(#421));
#421 = FILL_AREA_STYLE_COLOUR('',#422);
#422 = DRAUGHTING_PRE_DEFINED_COLOUR('yellow');
#423 = CURVE_STYLE('',#424,POSITIVE_LENGTH_MEASURE(0.1),#413);
#424 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
#425 = MECHANICAL_DESIGN_GEOMETRIC_PRESENTATION_REPRESENTATION('',(#426)
  ,#391);
#426 = STYLED_ITEM('color',(#427),#293);
#427 = PRESENTATION_STYLE_ASSIGNMENT((#428,#434));
#428 = SURFACE_STYLE_USAGE(.BOTH.,#429);
#429 = SURFACE_SIDE_STYLE('',(#430));
#430 = SURFACE_STYLE_FILL_AREA(#431);
#431 = FILL_AREA_STYLE('',(#432));
#432 = FILL_AREA_STYLE_COLOUR('',#433);
#433 = COLOUR_RGB('',0.333333345507,0.666666687201,1.);
#434 = CURVE_STYLE('',#435,POSITIVE_LENGTH_MEASURE(0.1),#413);
#435 = DRAUGHTING_PRE_DEFINED_CURVE_FONT('continuous');
ENDSEC;
END-ISO-10303-21;
