cmake_minimum_required(VERSION 3.16)

project(Qt5_3DSurfaceReconstruction VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${PROJECT_SOURCE_DIR}/bin")
set(CMAKE_INCLUDE_CURRENT_DIR ON)

# Qt
set(CMAKE_PREFIX_PATH "D:/qt5.15.2/5.15.2/mingw81_64")
find_package(QT NAMES Qt5 REQUIRED COMPONENTS Widgets)
find_package(Qt5 REQUIRED COMPONENTS
    Core
    Gui
    Widgets
    Gui
    Charts
    PrintSupport
)

# 3rdparty dependences
list(APPEND CMAKE_PREFIX_PATH "${PROJECT_SOURCE_DIR}/3rdparty/eigen3")
list(APPEND CMAKE_PREFIX_PATH "${PROJECT_SOURCE_DIR}/3rdparty/openblas")
list(APPEND CMAKE_PREFIX_PATH "${PROJECT_SOURCE_DIR}/3rdparty/nanoflann")
find_package(Eigen3 REQUIRED)
find_package(OpenBLAS REQUIRED)
find_package(nanoflann REQUIRED)
message(STATUS "Eigen3=${Eigen3_FOUND},path=${Eigen3_DIR}")
message(STATUS "OpenBLAS=${OpenBLAS_DIR}")
message(STATUS "nanoflann=${nanoflann_DIR}")
message(STATUS "CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}")

# Find package VTK
set(VTK_QT_VERSION 5)
#set(VTK_DIR "${PROJECT_SOURCE_DIR}/3rdparty/VTK9.0/lib/cmake/vtk-9.0")
set(VTK_DIR "D:/project/Qt5_3DSurfaceReconstruction/3rdparty/VTK9.0/lib/cmake/vtk-9.0")
# set(Qt5_DIR "D:\\Programs\\Qt\\5.15.2\\mingw81_64\\lib\\cmake\\Qt5")
# set(Qt6_DIR "D:\\Programs\\Qt6\\6.7.2\\mingw_64\\lib\\cmake\\Qt6")
find_package(VTK REQUIRED
    CommonCore          # 核心基础
    CommonDataModel     # 数据结构
    IOPLY
    IOImage
    RenderingVolume
    ImagingGeneral
    CommonExecutionModel
    InteractionStyle    # 交互
    RenderingCore       # 渲染核心
    RenderingOpenGL2
    GUISupportQt
    FiltersModeling
)

set(PROJECT_SOURCES
    fam.cpp
)

#NDI
set(NDI_INCLUDE_DIR "${PROJECT_SOURCE_DIR}/3rdparty/ndi/include")
set(NDI_LIBRARY_DEBUG "${PROJECT_SOURCE_DIR}/3rdparty/ndi/lib/debug/libNDIDriver.a")
set(NDI_LIBRARY_RELEASE "${PROJECT_SOURCE_DIR}/3rdparty/ndi/lib/release/libNDIDriver.a")
add_library(NDI::NDIDriver STATIC IMPORTED)
set_target_properties(NDI::NDIDriver PROPERTIES
        IMPORTED_LOCATION_DEBUG "${NDI_LIBRARY_DEBUG}"
        IMPORTED_LOCATION_RELEASE "${NDI_LIBRARY_RELEASE}"
        #IMPORTED_IMPLIB "${NDI_LIBRARY_RELEASE}" # 静态库不需要
        INTERFACE_INCLUDE_DIRECTORIES "${NDI_INCLUDE_DIR}"
        IMPORTED_CONFIGURATIONS "Debug;Release"
)

# TBB
set(TBB_ROOT  "${PROJECT_SOURCE_DIR}/3rdparty/TBB")
set(TBB_INCLUDE_DIR "${TBB_ROOT}/include")
set(TBB_LIBRARY_DIR "${TBB_ROOT}/lib")
include_directories("${TBB_INCLUDE_DIR}")
link_directories("${TBB_LIBRARY_DIR}")

#QCustomPlot
set(QCUSTOMPLOT_DIR "${PROJECT_SOURCE_DIR}/3rdparty/qcustomplot")
set(QCUSTOMPLOT_HEADER "${QCUSTOMPLOT_DIR}/qcustomplot.h")
set(QCUSTOMPLOT_SOURCE "${QCUSTOMPLOT_DIR}/qcustomplot.cpp")
message(STATUS "QCUSTOMPLOT_HEADER=${QCUSTOMPLOT_HEADER}")
message(STATUS "QCUSTOMPLOT_SOURCE=${QCUSTOMPLOT_SOURCE}")
set(QCUSTOMPLOT_OUTPUT_ROOT "${QCUSTOMPLOT_DIR}")
list(APPEND PROJECT_SOURCES "${QCUSTOMPLOT_SOURCE}")
list(APPEND PROJECT_SOURCES "${QCUSTOMPLOT_HEADER}")
include_directories("${QCUSTOMPLOT_DIR}")

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(Qt5_3DSurfaceReconstruction
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )
# Define target properties for Android with Qt 6 as:
#    set_property(TARGET Qt5_3DSurfaceReconstruction APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
#                 ${CMAKE_CURRENT_SOURCE_DIR}/android)
# For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(Qt5_3DSurfaceReconstruction SHARED
            ${PROJECT_SOURCES}
        )
# Define properties for Android with Qt 5 after find_package() calls as:
#    set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(Qt5_3DSurfaceReconstruction
            ${PROJECT_SOURCES}
            catheter.cpp catheter.h
            catheter_visualizer.cpp catheter_visualizer.h
            fam.cpp
            mainwindow.cpp mainwindow.h mainwindow.ui
            point.h
            reconstructor.cpp reconstructor.h
            RespEmitter.h
            respiration.cpp respiration.h
            scenewidget.cpp scenewidget.h
            volume.cpp volume.h
        )
    endif()
endif()

target_link_libraries(Qt5_3DSurfaceReconstruction PRIVATE
    Qt5::Widgets
    # Qt5::OpenGL
    Qt5::Charts
    Qt5::PrintSupport
    # ${VTK_LIBRARIES}
    VTK::CommonDataModel
    VTK::CommonExecutionModel
    VTK::IOPLY
    VTK::IOImage
    VTK::RenderingVolume
    VTK::ImagingStencil
    VTK::ImagingGeneral
    VTK::InteractionStyle
    VTK::RenderingCore
    VTK::RenderingOpenGL2
    VTK::GUISupportQt
    VTK::opengl
    VTK::FiltersModeling
    VTK::CommonCore
    OpenBLAS::OpenBLAS
    Eigen3::Eigen
    nanoflann::nanoflann
    tbb
    NDI::NDIDriver
    # fmt
    atomic
    # -lstdc++fs
)
target_compile_options(Qt5_3DSurfaceReconstruction
        PRIVATE
        $<$<CXX_COMPILER_ID:MSVC>:/bigobj,/std:c++17>
        $<$<CXX_COMPILER_ID:GNU>:-Wa,-mbig-obj>
)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
  set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.Qt5_3DSurfaceReconstruction)
endif()
set_target_properties(Qt5_3DSurfaceReconstruction PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS Qt5_3DSurfaceReconstruction
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(Qt5_3DSurfaceReconstruction)
endif()
